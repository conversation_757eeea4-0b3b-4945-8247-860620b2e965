package lk.sout.general.accounting.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Unified Account entity for managing all types of accounts including bank accounts, supplier accounts, etc.
 * Combines functionality from both core and accounting packages
 */
@Document
@Component
public class Account {

    @Id
    private String id;

    // Chart of Accounts fields
    @Indexed(unique = true)
    private String accountCode;

    private String accountName;

    private String accountNo;

    private String accountType; // BANK, SUPPLIER, CUSTOMER, CASH, PETTY_CASH

    private String accountCategory; // Current Asset, Fixed Asset, Current Liability, etc.

    private String parentAccountId; // For hierarchical account structure

    private boolean isSystemAccount; // For system-generated accounts

    // Bank specific fields
    private String bankName;

    private String branch;

    private String accountHolderName;

    // Supplier/Customer specific fields
    private String supplierId;

    private String customerId;

    private String supplierName;

    private String customerName;

    // Financial fields
    private Double currentBalance;

    private Double openingBalance;

    private String remark;

    private boolean active;

    // Audit fields
    private String createdBy;

    private LocalDateTime createdDate;

    private String lastModifiedBy;

    private LocalDateTime lastModifiedDate;

    // Constructors
    public Account() {
        this.active = true;
        this.currentBalance = 0.0;
        this.openingBalance = 0.0;
        this.createdDate = LocalDateTime.now();
        this.lastModifiedDate = LocalDateTime.now();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountCategory() {
        return accountCategory;
    }

    public void setAccountCategory(String accountCategory) {
        this.accountCategory = accountCategory;
    }

    public String getParentAccountId() {
        return parentAccountId;
    }

    public void setParentAccountId(String parentAccountId) {
        this.parentAccountId = parentAccountId;
    }

    public boolean isSystemAccount() {
        return isSystemAccount;
    }

    public void setSystemAccount(boolean systemAccount) {
        isSystemAccount = systemAccount;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getAccountHolderName() {
        return accountHolderName;
    }

    public void setAccountHolderName(String accountHolderName) {
        this.accountHolderName = accountHolderName;
    }

    public String getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(String supplierId) {
        this.supplierId = supplierId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Double getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(Double currentBalance) {
        this.currentBalance = currentBalance;
    }

    public Double getOpeningBalance() {
        return openingBalance;
    }

    public void setOpeningBalance(Double openingBalance) {
        this.openingBalance = openingBalance;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    // Account Type Constants
    public static final String ACCOUNT_TYPE_BANK = "BANK";
    public static final String ACCOUNT_TYPE_SUPPLIER = "SUPPLIER";
    public static final String ACCOUNT_TYPE_CUSTOMER = "CUSTOMER";
    public static final String ACCOUNT_TYPE_CASH = "CASH";
    public static final String ACCOUNT_TYPE_PETTY_CASH = "PETTY_CASH";

    // Account Category Constants
    public static final String CATEGORY_CURRENT_ASSET = "Current Asset";
    public static final String CATEGORY_FIXED_ASSET = "Fixed Asset";
    public static final String CATEGORY_CURRENT_LIABILITY = "Current Liability";
    public static final String CATEGORY_LONG_TERM_LIABILITY = "Long Term Liability";
    public static final String CATEGORY_EQUITY = "Equity";
    public static final String CATEGORY_REVENUE = "Revenue";
    public static final String CATEGORY_EXPENSE = "Expense";
}
