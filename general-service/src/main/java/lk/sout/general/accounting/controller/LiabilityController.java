package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.entity.Liability;
import lk.sout.business.accounting.service.LiabilityService;
import lk.sout.core.entity.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/liability")
public class LiabilityController {

    @Autowired
    private LiabilityService liabilityService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Liability liability) {
        try {
            boolean result = liabilityService.save(liability);
            if (result) {
                Response response = new Response(200, "Liability saved successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to save liability", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error saving liability: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAll(@RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize);
            return ResponseEntity.ok(liabilityService.findAll(pageable));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(liabilityService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByType", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByType(@RequestParam("liabilityType") String liabilityType) {
        try {
            return ResponseEntity.ok(liabilityService.findByType(liabilityType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByStatus(@RequestParam("status") String status) {
        try {
            return ResponseEntity.ok(liabilityService.findByStatus(status));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findOverdue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findOverdue() {
        try {
            return ResponseEntity.ok(liabilityService.findOverdueLiabilities());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/makePayment", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> makePayment(@RequestBody Map<String, Object> request) {
        try {
            String id = (String) request.get("id");
            Double paymentAmount = Double.valueOf(request.get("paymentAmount").toString());
            Date paymentDate = new Date((Long) request.get("paymentDate"));
            String notes = (String) request.get("notes");
            
            boolean result = liabilityService.makePayment(id, paymentAmount, paymentDate, notes);
            if (result) {
                Response response = new Response(200, "Payment recorded successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to record payment", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error recording payment: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/getTotalValue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getTotalValue() {
        try {
            return ResponseEntity.ok(liabilityService.getTotalLiabilityValue());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getByTypeValue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getByTypeValue() {
        try {
            return ResponseEntity.ok(liabilityService.getLiabilitiesByTypeValue());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/calculateInterest", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> calculateInterest(@RequestBody Map<String, String> request) {
        try {
            String id = request.get("id");
            boolean result = liabilityService.calculateInterest(id);
            if (result) {
                Response response = new Response(200, "Interest calculated successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to calculate interest", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error calculating interest: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/getUpcomingPayments", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getUpcomingPayments(@RequestParam("days") int days) {
        try {
            return ResponseEntity.ok(liabilityService.findUpcomingPayments(days));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/current", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getCurrentLiabilities() {
        try {
            return ResponseEntity.ok(liabilityService.findCurrentLiabilities());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/longTerm", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getLongTermLiabilities() {
        try {
            return ResponseEntity.ok(liabilityService.findLongTermLiabilities());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
