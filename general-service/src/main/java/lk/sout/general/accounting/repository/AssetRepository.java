package lk.sout.general.accounting.repository;

import lk.sout.business.accounting.entity.Asset;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface AssetRepository extends MongoRepository<Asset, String> {
    
    Page<Asset> findByActiveTrue(Pageable pageable);
    
    List<Asset> findByAssetTypeAndActiveTrue(String assetType);
    
    List<Asset> findByCategoryAndActiveTrue(String category);
    
    List<Asset> findByStatusAndActiveTrue(String status);
    
    @Query("{'assetType': 'CURRENT', 'active': true}")
    List<Asset> findCurrentAssets();
    
    @Query("{'assetType': 'FIXED', 'active': true}")
    List<Asset> findFixedAssets();
    
    @Query("{'status': 'ACTIVE', 'active': true}")
    List<Asset> findActiveAssets();
    
    @Query("{'status': 'DISPOSED', 'active': true}")
    List<Asset> findDisposedAssets();
    
    @Query("{'warrantyExpiry': {$lt: ?0}, 'active': true}")
    List<Asset> findAssetsWithExpiredWarranty(java.util.Date currentDate);
    
    @Query("{'active': true}")
    List<Asset> findAllActiveAssets();
}
