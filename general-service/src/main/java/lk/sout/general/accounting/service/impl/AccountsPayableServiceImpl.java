package lk.sout.general.accounting.service.impl;

import lk.sout.business.accounting.service.AccountsPayableService;
import lk.sout.business.trade.entity.PurchaseInvoice;
import lk.sout.business.trade.entity.Supplier;
import lk.sout.business.trade.repository.PurchaseInvoiceRepository;
import lk.sout.business.trade.service.PurchaseInvoiceService;
import lk.sout.business.trade.service.SupplierService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccountsPayableServiceImpl implements AccountsPayableService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsPayableServiceImpl.class);

    @Autowired
    private PurchaseInvoiceService purchaseInvoiceService;

    @Autowired
    private PurchaseInvoiceRepository purchaseInvoiceRepository;

    @Autowired
    private SupplierService supplierService;

    @Override
    public List<PurchaseInvoice> getOutstandingInvoices() {
        try {
            // Get all purchase invoices using repository directly to avoid pagination issues
            List<PurchaseInvoice> allInvoices = purchaseInvoiceRepository.findAllByOrderByIdDesc();
            LOGGER.info("AccountsPayableService: Found {} total purchase invoices", allInvoices.size());

            List<PurchaseInvoice> outstandingInvoices = allInvoices.stream()
                    .filter(invoice -> invoice.getBalance() != null && invoice.getBalance() > 0)
                    .sorted((a, b) -> b.getDate().compareTo(a.getDate()))
                    .collect(Collectors.toList());

            LOGGER.info("AccountsPayableService: Found {} outstanding purchase invoices", outstandingInvoices.size());
            return outstandingInvoices;
        } catch (Exception e) {
            LOGGER.error("Error getting outstanding purchase invoices: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PurchaseInvoice> getOutstandingInvoicesBySupplier(String supplierId) {
        try {
            Iterable<PurchaseInvoice> invoiceIterable = purchaseInvoiceService.findAll(0, 1000);
            List<PurchaseInvoice> allInvoices = new ArrayList<>();
            for (PurchaseInvoice invoice : invoiceIterable) {
                allInvoices.add(invoice);
            }
            return allInvoices.stream()
                    .filter(invoice -> invoice.getSupplier() != null &&
                                     supplierId.equals(invoice.getSupplier().getId()) &&
                                     invoice.getBalance() > 0)
                    .sorted((a, b) -> b.getDate().compareTo(a.getDate()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting outstanding invoices by supplier: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<Supplier, Double> getSupplierOutstandingBalances() {
        try {
            List<PurchaseInvoice> outstandingInvoices = getOutstandingInvoices();
            Map<Supplier, Double> supplierBalances = new HashMap<>();

            for (PurchaseInvoice invoice : outstandingInvoices) {
                if (invoice.getSupplier() != null) {
                    Supplier supplier = invoice.getSupplier();
                    supplierBalances.merge(supplier, invoice.getBalance(), Double::sum);
                }
            }

            // Sort by value in descending order
            List<Map.Entry<Supplier, Double>> sortedEntries = new ArrayList<>(supplierBalances.entrySet());
            sortedEntries.sort((e1, e2) -> e2.getValue().compareTo(e1.getValue()));

            LinkedHashMap<Supplier, Double> result = new LinkedHashMap<>();
            for (Map.Entry<Supplier, Double> entry : sortedEntries) {
                result.put(entry.getKey(), entry.getValue());
            }
            return result;
        } catch (Exception e) {
            LOGGER.error("Error getting supplier outstanding balances: " + e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, List<PurchaseInvoice>> getAgingAnalysis() {
        try {
            List<PurchaseInvoice> outstandingInvoices = getOutstandingInvoices();
            Map<String, List<PurchaseInvoice>> agingBuckets = new HashMap<>();
            
            agingBuckets.put("0-30 days", new ArrayList<>());
            agingBuckets.put("31-60 days", new ArrayList<>());
            agingBuckets.put("61-90 days", new ArrayList<>());
            agingBuckets.put("90+ days", new ArrayList<>());

            LocalDateTime now = LocalDateTime.now();

            for (PurchaseInvoice invoice : outstandingInvoices) {
                if (invoice.getDate() != null) {
                    long daysDiff = ChronoUnit.DAYS.between(invoice.getDate(), now.toLocalDate());

                    if (daysDiff <= 30) {
                        agingBuckets.get("0-30 days").add(invoice);
                    } else if (daysDiff <= 60) {
                        agingBuckets.get("31-60 days").add(invoice);
                    } else if (daysDiff <= 90) {
                        agingBuckets.get("61-90 days").add(invoice);
                    } else {
                        agingBuckets.get("90+ days").add(invoice);
                    }
                }
            }

            return agingBuckets;
        } catch (Exception e) {
            LOGGER.error("Error getting aging analysis: " + e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Double getTotalAccountsPayable() {
        try {
            List<PurchaseInvoice> outstandingInvoices = getOutstandingInvoices();
            return outstandingInvoices.stream()
                    .mapToDouble(invoice -> invoice.getBalance())
                    .sum();
        } catch (Exception e) {
            LOGGER.error("Error getting total accounts payable: " + e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    public List<PurchaseInvoice> getOverdueInvoices() {
        try {
            List<PurchaseInvoice> outstandingInvoices = getOutstandingInvoices();
            LocalDateTime now = LocalDateTime.now();

            return outstandingInvoices.stream()
                    .filter(invoice -> {
                        if (invoice.getDueDate() != null) {
                            return invoice.getDueDate().isBefore(now.toLocalDate());
                        }
                        // If no due date, consider invoices older than 30 days as overdue
                        if (invoice.getDate() != null) {
                            long daysDiff = ChronoUnit.DAYS.between(invoice.getDate(), now.toLocalDate());
                            return daysDiff > 30;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting overdue invoices: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PurchaseInvoice> getInvoicesByAgingPeriod(int days) {
        try {
            List<PurchaseInvoice> outstandingInvoices = getOutstandingInvoices();
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);

            return outstandingInvoices.stream()
                    .filter(invoice -> invoice.getDate() != null &&
                                     invoice.getDate().isBefore(cutoffDate))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting invoices by aging period: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PurchaseInvoice> getSupplierPaymentHistory(String supplierId, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            Iterable<PurchaseInvoice> invoiceIterable = purchaseInvoiceService.findAll(0, 1000);
            List<PurchaseInvoice> allInvoices = new ArrayList<>();
            for (PurchaseInvoice invoice : invoiceIterable) {
                allInvoices.add(invoice);
            }
            return allInvoices.stream()
                    .filter(invoice -> invoice.getSupplier() != null &&
                                     supplierId.equals(invoice.getSupplier().getId()) &&
                                     invoice.getDate() != null &&
                                     !invoice.getDate().isBefore(startDate) &&
                                     !invoice.getDate().isAfter(endDate))
                    .sorted((a, b) -> b.getDate().compareTo(a.getDate()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting supplier payment history: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getTopSuppliersByOutstanding(int limit) {
        try {
            Map<Supplier, Double> supplierBalances = getSupplierOutstandingBalances();
            
            List<Map<String, Object>> result = new ArrayList<>();
            int count = 0;
            for (Map.Entry<Supplier, Double> entry : supplierBalances.entrySet()) {
                if (count >= limit) break;

                Map<String, Object> supplierData = new HashMap<>();
                Supplier supplier = entry.getKey();
                supplierData.put("supplierId", supplier.getId());
                supplierData.put("supplierName", supplier.getName());
                supplierData.put("outstandingAmount", entry.getValue());
                result.add(supplierData);
                count++;
            }
            return result;
        } catch (Exception e) {
            LOGGER.error("Error getting top suppliers by outstanding: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<PurchaseInvoice> getPaymentSchedule(int daysAhead) {
        try {
            List<PurchaseInvoice> outstandingInvoices = getOutstandingInvoices();
            LocalDateTime futureDate = LocalDateTime.now().plusDays(daysAhead);

            return outstandingInvoices.stream()
                    .filter(invoice -> invoice.getDueDate() != null &&
                                     !invoice.getDueDate().isAfter(futureDate.toLocalDate()))
                    .sorted((a, b) -> a.getDueDate().compareTo(b.getDueDate()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting payment schedule: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
