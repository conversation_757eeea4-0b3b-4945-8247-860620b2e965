package lk.sout.general.accounting.repository;

import lk.sout.business.accounting.entity.PettyCash;
import lk.sout.business.accounting.entity.PettyCashTransaction;
import lk.sout.core.entity.MetaData;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface PettyCashTransactionRepository extends MongoRepository<PettyCashTransaction, String> {

    List<PettyCashTransaction> findAllByPettyCashOrderByCreatedDateDesc(PettyCash pettyCash);

    List<PettyCashTransaction> findAllByPettyCashOrderByCreatedDateDesc(PettyCash pettyCash, Pageable pageable);

    List<PettyCashTransaction> findByPettyCashAndCreatedDateBetweenOrderByCreatedDateDesc(
            PettyCash pettyCash, LocalDateTime startDate, LocalDateTime endDate);

    List<PettyCashTransaction> findByPettyCashAndTransactionTypeOrderByCreatedDateDesc(
            PettyCash pettyCash, MetaData transactionType);

    List<PettyCashTransaction> findByPettyCashAndExpenseCategoryOrderByCreatedDateDesc(
            PettyCash pettyCash, MetaData expenseCategory);

    List<PettyCashTransaction> findByPettyCashAndOperatorOrderByCreatedDateDesc(
            PettyCash pettyCash, String operator);

    List<PettyCashTransaction> findByCreatedDateBetweenOrderByCreatedDateDesc(
            LocalDateTime startDate, LocalDateTime endDate);

    List<PettyCashTransaction> findByVoucherNoContainingIgnoreCase(String voucherNo);
}
