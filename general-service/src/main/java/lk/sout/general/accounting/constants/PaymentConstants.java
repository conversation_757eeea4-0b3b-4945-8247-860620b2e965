package lk.sout.general.accounting.constants;

/**
 * Payment method and type constants to replace MetaData usage
 * This will be used throughout the system for consistent payment handling
 */
public class PaymentConstants {

    // Payment Methods
    public static final String PAYMENT_METHOD_CASH = "CASH";
    public static final String PAYMENT_METHOD_BANK_TRANSFER = "BANK_TRANSFER";
    public static final String PAYMENT_METHOD_CHEQUE = "CHEQUE";
    public static final String PAYMENT_METHOD_PETTY_CASH = "PETTY_CASH";
    public static final String PAYMENT_METHOD_CARD = "CARD";

    // Payment Types for Sales
    public static final String SALES_PAYMENT_CASH = "SALES_CASH";
    public static final String SALES_PAYMENT_CARD = "SALES_CARD";
    public static final String SALES_PAYMENT_CHEQUE = "SALES_CHEQUE";

    // Payment Types for Purchases
    public static final String PURCHASE_PAYMENT_BANK_TRANSFER = "PURCHASE_BANK_TRANSFER";
    public static final String PURCHASE_PAYMENT_CHEQUE = "PURCHASE_CHEQUE";
    public static final String PURCHASE_PAYMENT_PETTY_CASH = "PURCHASE_PETTY_CASH";
    public static final String PURCHASE_PAYMENT_CASH = "PURCHASE_CASH";

    // Account Types
    public static final String ACCOUNT_TYPE_SAVINGS = "SAVINGS";
    public static final String ACCOUNT_TYPE_CURRENT = "CURRENT";
    public static final String ACCOUNT_TYPE_FIXED_DEPOSIT = "FIXED_DEPOSIT";
    public static final String ACCOUNT_TYPE_FUND = "FUND";

    // Transaction Types
    public static final String TRANSACTION_TYPE_DEBIT = "DEBIT";
    public static final String TRANSACTION_TYPE_CREDIT = "CREDIT";

    // Cash Record Types
    public static final String CASH_TYPE_IN = "CASH_IN";
    public static final String CASH_TYPE_OUT = "CASH_OUT";

    // Cash Record Purposes
    public static final String CASH_PURPOSE_DAY_START = "DAY_START";
    public static final String CASH_PURPOSE_DAY_END = "DAY_END";
    public static final String CASH_PURPOSE_PURCHASING = "PURCHASING";
    public static final String CASH_PURPOSE_SALES = "SALES";
    public static final String CASH_PURPOSE_WITHDRAWAL = "WITHDRAWAL";

    // Cheque Status
    public static final String CHEQUE_STATUS_PENDING = "PENDING";
    public static final String CHEQUE_STATUS_CLEARED = "CLEARED";
    public static final String CHEQUE_STATUS_BOUNCED = "BOUNCED";
    public static final String CHEQUE_STATUS_CANCELLED = "CANCELLED";

    // Payment Status
    public static final String PAYMENT_STATUS_PENDING = "PENDING";
    public static final String PAYMENT_STATUS_PAID = "PAID";
    public static final String PAYMENT_STATUS_PARTIAL = "PARTIAL";
    public static final String PAYMENT_STATUS_CANCELLED = "CANCELLED";

    // Reference Types
    public static final String REFERENCE_TYPE_PURCHASE_INVOICE = "PURCHASE_INVOICE";
    public static final String REFERENCE_TYPE_SALES_INVOICE = "SALES_INVOICE";
    public static final String REFERENCE_TYPE_DEPOSIT = "DEPOSIT";
    public static final String REFERENCE_TYPE_WITHDRAWAL = "WITHDRAWAL";
    public static final String REFERENCE_TYPE_OPENING_BALANCE = "OPENING_BALANCE";

    // Bank Transaction Types
    public static final String BANK_TRANSACTION_PURCHASE_PAYMENT = "PURCHASE_PAYMENT";
    public static final String BANK_TRANSACTION_DEPOSIT = "DEPOSIT";
    public static final String BANK_TRANSACTION_WITHDRAWAL = "WITHDRAWAL";
    public static final String BANK_TRANSACTION_TRANSFER = "TRANSFER";
    public static final String BANK_TRANSACTION_OPENING_BALANCE = "OPENING_BALANCE";

    // Validation Messages
    public static final String MSG_INSUFFICIENT_BALANCE = "Insufficient balance";
    public static final String MSG_INVALID_PAYMENT_METHOD = "Invalid payment method";
    public static final String MSG_PAYMENT_SUCCESSFUL = "Payment processed successfully";
    public static final String MSG_PAYMENT_FAILED = "Payment processing failed";

    // Private constructor to prevent instantiation
    private PaymentConstants() {
        throw new IllegalStateException("Utility class");
    }
}
