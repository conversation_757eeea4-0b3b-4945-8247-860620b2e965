package lk.sout.general.accounting.service.impl;

import lk.sout.business.accounting.entity.PettyCash;
import lk.sout.business.accounting.entity.PettyCashTransaction;
import lk.sout.business.accounting.repository.PettyCashRepository;
import lk.sout.business.accounting.repository.PettyCashTransactionRepository;
import lk.sout.business.accounting.service.PettyCashService;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.entity.Sequence;
import lk.sout.core.service.MetaDataService;
import lk.sout.core.service.SequenceService;
import lk.sout.core.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class PettyCashServiceImpl implements PettyCashService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PettyCashServiceImpl.class);

    @Autowired
    private PettyCashRepository pettyCashRepository;

    @Autowired
    private PettyCashTransactionRepository pettyCashTransactionRepository;

    @Autowired
    private MetaDataService metaDataService;

    @Autowired
    private SequenceService sequenceService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private Response response;

    @Override
    @Transactional
    public Response save(PettyCash pettyCash) {
        try {
            if (pettyCash.getId() == null || pettyCash.getId().isEmpty()) {
                // Generate petty cash number for new records
                Sequence sequence = sequenceService.findSequenceByName("PettyCash");
                if (sequence != null) {
                    String pettyCashNo = sequence.getPrefix() + String.format("%04d", sequence.getCounter() + 1);
                    pettyCash.setPettyCashNo(pettyCashNo);

                    // Increment the sequence counter
                    sequence.setCounter(sequence.getCounter() + 1);
                    sequenceService.save(sequence);
                } else {
                    // Create default sequence if not found
                    sequenceService.saveIfUnavailable("PettyCash", "PC", 1);
                    String pettyCashNo = "PC0001";
                    pettyCash.setPettyCashNo(pettyCashNo);
                }
                
                // Set opening balance as current balance for new petty cash
                if (pettyCash.getCurrentBalance() == null) {
                    pettyCash.setCurrentBalance(pettyCash.getOpeningBalance() != null ? 
                                              pettyCash.getOpeningBalance() : 0.0);
                }
            }

            PettyCash saved = pettyCashRepository.save(pettyCash);
            
            if (saved != null) {
                response.setCode(200);
                response.setMessage("Petty cash saved successfully");
                response.setData(saved.getId());
            } else {
                response.setCode(500);
                response.setMessage("Failed to save petty cash");
            }
        } catch (Exception e) {
            LOGGER.error("Error saving petty cash: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error saving petty cash: " + e.getMessage());
        }
        return response;
    }

    @Override
    public PettyCash basicSave(PettyCash pettyCash) {
        try {
            return pettyCashRepository.save(pettyCash);
        } catch (Exception e) {
            LOGGER.error("Error in basic save: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCash> findAllActive() {
        try {
            return pettyCashRepository.findAllByActiveTrue();
        } catch (Exception e) {
            LOGGER.error("Error finding active petty cash: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public PettyCash findByPettyCashNo(String pettyCashNo) {
        try {
            return pettyCashRepository.findByPettyCashNo(pettyCashNo);
        } catch (Exception e) {
            LOGGER.error("Error finding petty cash by number: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public PettyCash findById(String id) {
        try {
            return pettyCashRepository.findById(id).orElse(null);
        } catch (Exception e) {
            LOGGER.error("Error finding petty cash by ID: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCash> findByLocation(String location) {
        try {
            return pettyCashRepository.findByLocationContainingIgnoreCase(location);
        } catch (Exception e) {
            LOGGER.error("Error finding petty cash by location: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCash> findByCustodian(String custodian) {
        try {
            return pettyCashRepository.findByCustodianContainingIgnoreCase(custodian);
        } catch (Exception e) {
            LOGGER.error("Error finding petty cash by custodian: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    @Transactional
    public Response createTransaction(PettyCashTransaction transaction) {
        try {
            // Validate petty cash exists
            PettyCash pettyCash = findById(transaction.getPettyCash().getId());
            if (pettyCash == null) {
                response.setCode(404);
                response.setMessage("Petty cash not found");
                return response;
            }

            // Validate sufficient balance for expenses
            if ("-".equals(transaction.getOperator()) && 
                !validateSufficientBalance(pettyCash.getId(), transaction.getAmount())) {
                response.setCode(400);
                response.setMessage("Insufficient petty cash balance");
                return response;
            }

            // Update balance
            Double newBalance = updateBalanceAndGet(pettyCash, transaction.getAmount(), transaction.getOperator());
            transaction.setBalanceAfter(newBalance);

            // Save transaction
            PettyCashTransaction saved = pettyCashTransactionRepository.save(transaction);

            if (saved != null) {
                response.setCode(200);
                response.setMessage("Petty cash transaction recorded successfully");
                response.setData(saved.getId());
            } else {
                response.setCode(500);
                response.setMessage("Failed to record petty cash transaction");
            }
        } catch (Exception e) {
            LOGGER.error("Error creating petty cash transaction: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error creating transaction: " + e.getMessage());
        }
        return response;
    }

    @Override
    @Transactional
    public Response replenishPettyCash(String pettyCashId, Double amount, String description, String voucherNo) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            if (pettyCash == null) {
                response.setCode(404);
                response.setMessage("Petty cash not found");
                return response;
            }

            MetaData replenishmentType = metaDataService.searchMetaData("Replenishment", "PettyCashTransactionType");

            PettyCashTransaction transaction = new PettyCashTransaction();
            transaction.setPettyCash(pettyCash);
            transaction.setTransactionType(replenishmentType);
            transaction.setAmount(amount);
            transaction.setDescription(description);
            transaction.setVoucherNo(voucherNo);
            transaction.setOperator("+");

            return createTransaction(transaction);
        } catch (Exception e) {
            LOGGER.error("Error replenishing petty cash: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error replenishing petty cash: " + e.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response recordExpense(String pettyCashId, Double amount, String description,
                                 String expenseCategoryId, String voucherNo, String receivedBy) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            if (pettyCash == null) {
                response.setCode(404);
                response.setMessage("Petty cash not found");
                return response;
            }

            MetaData expenseType = metaDataService.searchMetaData("Expense", "PettyCashTransactionType");
            MetaData expenseCategory = metaDataService.findById(expenseCategoryId);

            PettyCashTransaction transaction = new PettyCashTransaction();
            transaction.setPettyCash(pettyCash);
            transaction.setTransactionType(expenseType);
            transaction.setExpenseCategory(expenseCategory);
            transaction.setAmount(amount);
            transaction.setDescription(description);
            transaction.setVoucherNo(voucherNo);
            transaction.setReceivedBy(receivedBy);
            transaction.setOperator("-");

            Response result = createTransaction(transaction);

            // Create main transaction record for expense tracking
            if (result.getCode() == 200) {
                transactionService.createTransaction(
                    amount,
                    "-",
                    voucherNo,
                    "Petty Cash Expense",
                    receivedBy,
                    "Expense",
                    "Other",
                    "Petty Cash",
                    description,
                    LocalDateTime.now()
                );
            }

            return result;
        } catch (Exception e) {
            LOGGER.error("Error recording petty cash expense: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error recording expense: " + e.getMessage());
            return response;
        }
    }

    @Override
    @Transactional
    public Response recordRefund(String pettyCashId, Double amount, String description, String voucherNo) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            if (pettyCash == null) {
                response.setCode(404);
                response.setMessage("Petty cash not found");
                return response;
            }

            MetaData refundType = metaDataService.searchMetaData("Refund", "PettyCashTransactionType");

            PettyCashTransaction transaction = new PettyCashTransaction();
            transaction.setPettyCash(pettyCash);
            transaction.setTransactionType(refundType);
            transaction.setAmount(amount);
            transaction.setDescription(description);
            transaction.setVoucherNo(voucherNo);
            transaction.setOperator("+");

            return createTransaction(transaction);
        } catch (Exception e) {
            LOGGER.error("Error recording petty cash refund: " + e.getMessage(), e);
            response.setCode(500);
            response.setMessage("Error recording refund: " + e.getMessage());
            return response;
        }
    }

    // Transaction Query Methods
    @Override
    public List<PettyCashTransaction> findTransactionsByPettyCash(String pettyCashId) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            return pettyCash != null ? pettyCashTransactionRepository.findAllByPettyCashOrderByCreatedDateDesc(pettyCash) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by petty cash: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCashTransaction> findTransactionsByPettyCash(String pettyCashId, Pageable pageable) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            return pettyCash != null ? pettyCashTransactionRepository.findAllByPettyCashOrderByCreatedDateDesc(pettyCash, pageable) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by petty cash with pagination: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCashTransaction> findTransactionsByDateRange(String pettyCashId, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            return pettyCash != null ? pettyCashTransactionRepository.findByPettyCashAndCreatedDateBetweenOrderByCreatedDateDesc(pettyCash, startDate, endDate) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by date range: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCashTransaction> findTransactionsByType(String pettyCashId, String transactionTypeId) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            MetaData transactionType = metaDataService.findById(transactionTypeId);
            return (pettyCash != null && transactionType != null) ?
                   pettyCashTransactionRepository.findByPettyCashAndTransactionTypeOrderByCreatedDateDesc(pettyCash, transactionType) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by type: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCashTransaction> findTransactionsByExpenseCategory(String pettyCashId, String expenseCategoryId) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            MetaData expenseCategory = metaDataService.findById(expenseCategoryId);
            return (pettyCash != null && expenseCategory != null) ?
                   pettyCashTransactionRepository.findByPettyCashAndExpenseCategoryOrderByCreatedDateDesc(pettyCash, expenseCategory) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by expense category: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCashTransaction> findTransactionsByOperator(String pettyCashId, String operator) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            return pettyCash != null ? pettyCashTransactionRepository.findByPettyCashAndOperatorOrderByCreatedDateDesc(pettyCash, operator) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transactions by operator: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<PettyCashTransaction> findAllTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            return pettyCashTransactionRepository.findByCreatedDateBetweenOrderByCreatedDateDesc(startDate, endDate);
        } catch (Exception e) {
            LOGGER.error("Error finding all transactions by date range: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public PettyCashTransaction findTransactionByVoucherNo(String voucherNo) {
        try {
            List<PettyCashTransaction> transactions = pettyCashTransactionRepository.findByVoucherNoContainingIgnoreCase(voucherNo);
            return transactions != null && !transactions.isEmpty() ? transactions.get(0) : null;
        } catch (Exception e) {
            LOGGER.error("Error finding transaction by voucher number: " + e.getMessage(), e);
            return null;
        }
    }

    // Balance Management Methods
    @Override
    public Double getCurrentBalance(String pettyCashId) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            return pettyCash != null ? pettyCash.getCurrentBalance() : 0.0;
        } catch (Exception e) {
            LOGGER.error("Error getting current balance: " + e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    @Transactional
    public boolean updateBalance(String pettyCashId, Double amount, String operator) {
        try {
            PettyCash pettyCash = findById(pettyCashId);
            if (pettyCash == null) {
                return false;
            }

            Double currentBalance = pettyCash.getCurrentBalance();
            Double newBalance;

            if ("+".equals(operator)) {
                newBalance = currentBalance + amount;
            } else if ("-".equals(operator)) {
                newBalance = currentBalance - amount;
                if (newBalance < 0) {
                    LOGGER.warn("Negative balance detected for petty cash: " + pettyCashId);
                    return false;
                }
            } else {
                LOGGER.error("Invalid operator: " + operator);
                return false;
            }

            pettyCash.setCurrentBalance(newBalance);
            PettyCash saved = basicSave(pettyCash);
            return saved != null;
        } catch (Exception e) {
            LOGGER.error("Error updating balance: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean validateSufficientBalance(String pettyCashId, Double amount) {
        try {
            Double currentBalance = getCurrentBalance(pettyCashId);
            return currentBalance >= amount;
        } catch (Exception e) {
            LOGGER.error("Error validating balance: " + e.getMessage(), e);
            return false;
        }
    }

    // Helper method for updating balance and returning new balance
    private Double updateBalanceAndGet(PettyCash pettyCash, Double amount, String operator) {
        Double currentBalance = pettyCash.getCurrentBalance();
        Double newBalance;

        if ("+".equals(operator)) {
            newBalance = currentBalance + amount;
        } else {
            newBalance = currentBalance - amount;
        }

        pettyCash.setCurrentBalance(newBalance);
        basicSave(pettyCash);
        return newBalance;
    }
}
