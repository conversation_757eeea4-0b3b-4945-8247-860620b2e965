package lk.sout.general.accounting.service;

import lk.sout.business.trade.entity.PurchaseInvoice;
import lk.sout.business.trade.entity.Supplier;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AccountsPayableService {

    /**
     * Get all outstanding purchase invoices (balance > 0)
     */
    List<PurchaseInvoice> getOutstandingInvoices();

    /**
     * Get outstanding invoices by supplier
     */
    List<PurchaseInvoice> getOutstandingInvoicesBySupplier(String supplierId);

    /**
     * Get supplier-wise outstanding balances
     */
    Map<Supplier, Double> getSupplierOutstandingBalances();

    /**
     * Get aging analysis (30, 60, 90+ days)
     */
    Map<String, List<PurchaseInvoice>> getAgingAnalysis();

    /**
     * Get total accounts payable amount
     */
    Double getTotalAccountsPayable();

    /**
     * Get overdue invoices
     */
    List<PurchaseInvoice> getOverdueInvoices();

    /**
     * Get invoices by aging period
     */
    List<PurchaseInvoice> getInvoicesByAgingPeriod(int days);

    /**
     * Get supplier payment history
     */
    List<PurchaseInvoice> getSupplierPaymentHistory(String supplierId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Get top suppliers by outstanding amount
     */
    List<Map<String, Object>> getTopSuppliersByOutstanding(int limit);

    /**
     * Get payment schedule (upcoming due dates)
     */
    List<PurchaseInvoice> getPaymentSchedule(int daysAhead);
}
