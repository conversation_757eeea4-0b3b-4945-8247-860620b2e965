package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.entity.Account;
import lk.sout.business.accounting.entity.AccountTransaction;
import lk.sout.business.accounting.service.AccountService;
import lk.sout.core.entity.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/account")
@CrossOrigin
public class AccountController {

    @Autowired
    private AccountService accountService;

    // Account Management Endpoints

    @PostMapping("/save")
    public ResponseEntity<Response> save(@RequestBody Account account) {
        try {
            Response response = accountService.save(account);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response errorResponse = new Response();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("Error saving account: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @GetMapping("/findAllAccounts")
    public ResponseEntity<List<Account>> findAllAccounts() {
        try {
            List<Account> accounts = accountService.findAllActive();
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findById")
    public ResponseEntity<Account> findById(@RequestParam String id) {
        try {
            Account account = accountService.findById(id);
            if (account != null) {
                return ResponseEntity.ok(account);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<Account> findByAccountNo(@RequestParam String any) {
        try {
            Account account = accountService.findByAccountNo(any);
            if (account != null) {
                return ResponseEntity.ok(account);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByAccountCode")
    public ResponseEntity<Account> findByAccountCode(@RequestParam String accountCode) {
        try {
            Account account = accountService.findByAccountCode(accountCode);
            if (account != null) {
                return ResponseEntity.ok(account);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findAllPage")
    public ResponseEntity<Page<Account>> findAllPage(@RequestParam int page, @RequestParam int pageSize) {
        try {
            Page<Account> accounts = accountService.findAllPage(page, pageSize);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByBankName")
    public ResponseEntity<List<Account>> findByBankName(@RequestParam String bankName) {
        try {
            List<Account> accounts = accountService.findByBankName(bankName);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByAccountHolderName")
    public ResponseEntity<List<Account>> findByAccountHolderName(@RequestParam String accountHolderName) {
        try {
            List<Account> accounts = accountService.findByAccountHolderName(accountHolderName);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByAccountName")
    public ResponseEntity<List<Account>> findByAccountName(@RequestParam String accountName) {
        try {
            List<Account> accounts = accountService.findByAccountName(accountName);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByAccountType")
    public ResponseEntity<List<Account>> findByAccountType(@RequestParam String accountType) {
        try {
            List<Account> accounts = accountService.findByAccountType(accountType);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByAccountCategory")
    public ResponseEntity<List<Account>> findByAccountCategory(@RequestParam String accountCategory) {
        try {
            List<Account> accounts = accountService.findByAccountCategory(accountCategory);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findBySupplierName")
    public ResponseEntity<List<Account>> findBySupplierName(@RequestParam String supplierName) {
        try {
            List<Account> accounts = accountService.findBySupplierName(supplierName);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findByCustomerName")
    public ResponseEntity<List<Account>> findByCustomerName(@RequestParam String customerName) {
        try {
            List<Account> accounts = accountService.findByCustomerName(customerName);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Transaction Management Endpoints

    @PostMapping("/createTransaction")
    public ResponseEntity<Response> createTransaction(@RequestBody AccountTransaction transaction) {
        try {
            Response response = accountService.createTransaction(transaction);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response errorResponse = new Response();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("Error creating transaction: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @GetMapping("/findTransactionsByAccount")
    public ResponseEntity<List<AccountTransaction>> findTransactionsByAccount(@RequestParam String accountId) {
        try {
            List<AccountTransaction> transactions = accountService.findTransactionsByAccount(accountId);
            return ResponseEntity.ok(transactions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findTransactionsByReferenceNo")
    public ResponseEntity<List<AccountTransaction>> findTransactionsByReferenceNo(@RequestParam String referenceNo) {
        try {
            List<AccountTransaction> transactions = accountService.findTransactionsByReferenceNo(referenceNo);
            return ResponseEntity.ok(transactions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findTransactionsByDateRange")
    public ResponseEntity<List<AccountTransaction>> findTransactionsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            List<AccountTransaction> transactions = accountService.findTransactionsByDateRange(startDate, endDate);
            return ResponseEntity.ok(transactions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findTransactionsByAccountAndDateRange")
    public ResponseEntity<List<AccountTransaction>> findTransactionsByAccountAndDateRange(
            @RequestParam String accountId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            List<AccountTransaction> transactions = accountService.findTransactionsByAccountAndDateRange(
                    accountId, startDate, endDate);
            return ResponseEntity.ok(transactions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findTransactionsPage")
    public ResponseEntity<Page<AccountTransaction>> findTransactionsPage(
            @RequestParam String accountId,
            @RequestParam int page,
            @RequestParam int pageSize) {
        try {
            Page<AccountTransaction> transactions = accountService.findTransactionsPage(accountId, page, pageSize);
            return ResponseEntity.ok(transactions);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Balance Management Endpoints

    @GetMapping("/getCurrentBalance")
    public ResponseEntity<Double> getCurrentBalance(@RequestParam String accountId) {
        try {
            Double balance = accountService.getCurrentBalance(accountId);
            return ResponseEntity.ok(balance);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/updateBalance")
    public ResponseEntity<Boolean> updateBalance(@RequestParam String accountId,
                                                @RequestParam Double amount,
                                                @RequestParam String operator) {
        try {
            boolean result = accountService.updateBalance(accountId, amount, operator);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    @PostMapping("/debitAccount")
    public ResponseEntity<Boolean> debitAccount(@RequestParam String accountId,
                                               @RequestParam Double amount,
                                               @RequestParam String referenceNo,
                                               @RequestParam String referenceType,
                                               @RequestParam String description,
                                               @RequestParam(required = false) String thirdParty) {
        try {
            boolean result = accountService.debitAccount(accountId, amount, referenceNo, referenceType, description, thirdParty);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    @PostMapping("/creditAccount")
    public ResponseEntity<Boolean> creditAccount(@RequestParam String accountId,
                                                @RequestParam Double amount,
                                                @RequestParam String referenceNo,
                                                @RequestParam String referenceType,
                                                @RequestParam String description,
                                                @RequestParam(required = false) String thirdParty) {
        try {
            boolean result = accountService.creditAccount(accountId, amount, referenceNo, referenceType, description, thirdParty);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    @PostMapping("/transferFunds")
    public ResponseEntity<Response> transferFunds(@RequestParam String fromAccountId,
                                                 @RequestParam String toAccountId,
                                                 @RequestParam Double amount,
                                                 @RequestParam String description,
                                                 @RequestParam String referenceNo) {
        try {
            Response response = accountService.transferFunds(fromAccountId, toAccountId, amount, description, referenceNo);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response errorResponse = new Response();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("Error transferring funds: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @PostMapping("/processPurchasePayment")
    public ResponseEntity<Boolean> processPurchasePayment(@RequestParam String accountId,
                                                         @RequestParam Double amount,
                                                         @RequestParam String purchaseInvoiceNo,
                                                         @RequestParam String supplierName) {
        try {
            boolean result = accountService.processPurchasePayment(accountId, amount, purchaseInvoiceNo, supplierName);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    // Utility Endpoints

    @GetMapping("/getLastTransaction")
    public ResponseEntity<AccountTransaction> getLastTransaction(@RequestParam String accountId) {
        try {
            AccountTransaction transaction = accountService.getLastTransaction(accountId);
            if (transaction != null) {
                return ResponseEntity.ok(transaction);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/calculateBalanceFromTransactions")
    public ResponseEntity<Double> calculateBalanceFromTransactions(@RequestParam String accountId) {
        try {
            Double balance = accountService.calculateBalanceFromTransactions(accountId);
            return ResponseEntity.ok(balance);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/validateSufficientBalance")
    public ResponseEntity<Boolean> validateSufficientBalance(@RequestParam String accountId, @RequestParam Double amount) {
        try {
            boolean result = accountService.validateSufficientBalance(accountId, amount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(false);
        }
    }

    // Balance Correction Endpoint
    @PostMapping("/correctBalance")
    public ResponseEntity<Response> correctBalance(@RequestParam String accountId,
                                                  @RequestParam Double newBalance,
                                                  @RequestParam String reason,
                                                  @RequestParam String correctedBy) {
        try {
            Response response = accountService.correctBalance(accountId, newBalance, reason, correctedBy);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Response errorResponse = new Response();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("Error correcting balance: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    // Backward compatibility endpoints for legacy BankAccount references
    // These endpoints are kept for existing frontend code that still uses bank-specific endpoints

    @PostMapping("/saveBankAccount")
    public ResponseEntity<Response> saveBankAccount(@RequestBody Account account) {
        account.setAccountType(Account.ACCOUNT_TYPE_BANK);
        return save(account);
    }

    @GetMapping("/findAllBankAccounts")
    public ResponseEntity<List<Account>> findAllBankAccounts() {
        try {
            List<Account> accounts = accountService.findAllBankAccounts();
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/findBankAccountById")
    public ResponseEntity<Account> findBankAccountById(@RequestParam String id) {
        return findById(id);
    }

    @GetMapping("/findBankAccountByBankName")
    public ResponseEntity<List<Account>> findBankAccountByBankName(@RequestParam String bankName) {
        return findByBankName(bankName);
    }

    @GetMapping("/findBankAccountByAccountHolderName")
    public ResponseEntity<List<Account>> findBankAccountByAccountHolderName(@RequestParam String accountHolderName) {
        return findByAccountHolderName(accountHolderName);
    }

    @GetMapping("/findBankAccountByAccountName")
    public ResponseEntity<List<Account>> findBankAccountByAccountName(@RequestParam String accountName) {
        return findByAccountName(accountName);
    }

    @GetMapping("/findAllBankAccountsPage")
    public ResponseEntity<Page<Account>> findAllBankAccountsPage(@RequestParam int page, @RequestParam int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize);
            Page<Account> accounts = accountService.findAllBankAccountsPage(pageable);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
