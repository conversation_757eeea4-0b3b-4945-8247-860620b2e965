package lk.sout.general.accounting.service.impl;

import lk.sout.business.accounting.entity.Asset;
import lk.sout.business.accounting.repository.AssetRepository;
import lk.sout.business.accounting.service.AssetService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class AssetServiceImpl implements AssetService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AssetServiceImpl.class);

    @Autowired
    private AssetRepository assetRepository;

    @Override
    public boolean save(Asset asset) {
        try {
            if (asset.getId() == null) {
                // New asset
                asset.setCreatedDate(new Date());
                asset.setActive(true);
                asset.setCurrentValue(asset.getPurchaseValue());
            } else {
                // Update existing asset
                asset.setLastUpdated(new Date());
            }
            
            assetRepository.save(asset);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving asset failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<Asset> findAll(Pageable pageable) {
        try {
            return assetRepository.findByActiveTrue(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving assets failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Asset findById(String id) {
        try {
            Optional<Asset> asset = assetRepository.findById(id);
            return asset.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Retrieving asset by id failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findByType(String assetType) {
        try {
            return assetRepository.findByAssetTypeAndActiveTrue(assetType);
        } catch (Exception ex) {
            LOGGER.error("Retrieving assets by type failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findByCategory(String category) {
        try {
            return assetRepository.findByCategoryAndActiveTrue(category);
        } catch (Exception ex) {
            LOGGER.error("Retrieving assets by category failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findByStatus(String status) {
        try {
            return assetRepository.findByStatusAndActiveTrue(status);
        } catch (Exception ex) {
            LOGGER.error("Retrieving assets by status failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findCurrentAssets() {
        try {
            return assetRepository.findCurrentAssets();
        } catch (Exception ex) {
            LOGGER.error("Retrieving current assets failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findFixedAssets() {
        try {
            return assetRepository.findFixedAssets();
        } catch (Exception ex) {
            LOGGER.error("Retrieving fixed assets failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findActiveAssets() {
        try {
            return assetRepository.findActiveAssets();
        } catch (Exception ex) {
            LOGGER.error("Retrieving active assets failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findDisposedAssets() {
        try {
            return assetRepository.findDisposedAssets();
        } catch (Exception ex) {
            LOGGER.error("Retrieving disposed assets failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Asset> findAssetsWithExpiredWarranty() {
        try {
            return assetRepository.findAssetsWithExpiredWarranty(new Date());
        } catch (Exception ex) {
            LOGGER.error("Retrieving assets with expired warranty failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean calculateDepreciation(String id) {
        try {
            Optional<Asset> optionalAsset = assetRepository.findById(id);
            if (optionalAsset.isPresent()) {
                Asset asset = optionalAsset.get();
                Double currentValue = calculateCurrentValue(asset);
                asset.setCurrentValue(currentValue);
                asset.setLastUpdated(new Date());
                assetRepository.save(asset);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Calculating depreciation failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Double getTotalAssetValue() {
        try {
            List<Asset> assets = assetRepository.findAllActiveAssets();
            return assets.stream()
                    .mapToDouble(asset -> asset.getCurrentValue() != null ? asset.getCurrentValue() : 0.0)
                    .sum();
        } catch (Exception ex) {
            LOGGER.error("Calculating total asset value failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public Map<String, Double> getAssetsByTypeValue() {
        try {
            Map<String, Double> result = new HashMap<>();
            result.put("CURRENT", getTotalCurrentAssetsValue());
            result.put("FIXED", getTotalFixedAssetsValue());
            return result;
        } catch (Exception ex) {
            LOGGER.error("Getting assets by type value failed: " + ex.getMessage());
            return new HashMap<>();
        }
    }

    @Override
    public boolean disposeAsset(String id, Date disposalDate, Double disposalValue, String reason) {
        try {
            Optional<Asset> optionalAsset = assetRepository.findById(id);
            if (optionalAsset.isPresent()) {
                Asset asset = optionalAsset.get();
                asset.setStatus("DISPOSED");
                asset.setCurrentValue(disposalValue);
                asset.setLastUpdated(disposalDate);
                asset.setDescription(asset.getDescription() + " | Disposal Reason: " + reason);
                assetRepository.save(asset);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Disposing asset failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Double calculateCurrentValue(Asset asset) {
        try {
            if (asset.getPurchaseValue() == null || asset.getPurchaseDate() == null) {
                return asset.getPurchaseValue();
            }

            if ("CURRENT".equals(asset.getAssetType())) {
                // Current assets don't depreciate
                return asset.getPurchaseValue();
            }

            // Calculate depreciation for fixed assets
            Date currentDate = new Date();
            long diffInMillies = currentDate.getTime() - asset.getPurchaseDate().getTime();
            double yearsElapsed = diffInMillies / (365.25 * 24 * 60 * 60 * 1000);

            if ("STRAIGHT_LINE".equals(asset.getDepreciationMethod())) {
                double annualDepreciation = asset.getPurchaseValue() / asset.getUsefulLife();
                double totalDepreciation = annualDepreciation * yearsElapsed;
                return Math.max(0, asset.getPurchaseValue() - totalDepreciation);
            } else if ("DECLINING_BALANCE".equals(asset.getDepreciationMethod())) {
                double rate = asset.getDepreciationRate() / 100;
                return asset.getPurchaseValue() * Math.pow(1 - rate, yearsElapsed);
            }

            return asset.getPurchaseValue();
        } catch (Exception ex) {
            LOGGER.error("Calculating current value failed: " + ex.getMessage());
            return asset.getPurchaseValue();
        }
    }

    @Override
    public Double getTotalCurrentAssetsValue() {
        try {
            List<Asset> currentAssets = findCurrentAssets();
            if (currentAssets != null) {
                return currentAssets.stream()
                        .mapToDouble(asset -> asset.getCurrentValue() != null ? asset.getCurrentValue() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total current assets value failed: " + ex.getMessage());
            return 0.0;
        }
    }

    @Override
    public Double getTotalFixedAssetsValue() {
        try {
            List<Asset> fixedAssets = findFixedAssets();
            if (fixedAssets != null) {
                return fixedAssets.stream()
                        .mapToDouble(asset -> asset.getCurrentValue() != null ? asset.getCurrentValue() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total fixed assets value failed: " + ex.getMessage());
            return 0.0;
        }
    }
}
