package lk.sout.general.accounting.repository;

import lk.sout.business.accounting.entity.Account;
import lk.sout.business.accounting.entity.AccountTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AccountTransactionRepository extends MongoRepository<AccountTransaction, String> {

    // Basic account transaction queries
    List<AccountTransaction> findByAccountAndActiveTrue(Account account);
    
    List<AccountTransaction> findByAccountAndActiveTrueOrderByTransactionDateDesc(Account account);
    
    List<AccountTransaction> findByAccountAndActiveOrderByTransactionDateDesc(Account account, boolean active);
    
    List<AccountTransaction> findByAccountIdAndActiveOrderByTransactionDateDesc(String accountId, boolean active);

    // Reference queries
    List<AccountTransaction> findByReferenceNoAndActive(String referenceNo, boolean active);
    
    List<AccountTransaction> findByReferenceNoAndActiveTrue(String referenceNo);
    
    List<AccountTransaction> findByReferenceTypeAndActive(String referenceType, boolean active);
    
    List<AccountTransaction> findByReferenceTypeAndActiveTrue(String referenceType);

    // Payment type queries
    List<AccountTransaction> findByPaymentTypeAndActiveTrue(String paymentType);

    // Date range queries
    List<AccountTransaction> findByTransactionDateBetweenAndActiveOrderByTransactionDateDesc(
            LocalDateTime startDate, LocalDateTime endDate, boolean active);
    
    List<AccountTransaction> findByTransactionDateBetweenAndActiveTrue(LocalDateTime startDate, LocalDateTime endDate);

    // Account and date range queries
    List<AccountTransaction> findByAccountAndTransactionDateBetweenAndActiveOrderByTransactionDateDesc(
            Account account, LocalDateTime startDate, LocalDateTime endDate, boolean active);
    
    List<AccountTransaction> findByAccountAndTransactionDateBetweenAndActiveTrue(
            Account account, LocalDateTime startDate, LocalDateTime endDate);
    
    List<AccountTransaction> findByAccountIdAndTransactionDateBetweenAndActiveOrderByTransactionDateDesc(
            String accountId, LocalDateTime startDate, LocalDateTime endDate, boolean active);

    // Pagination queries
    Page<AccountTransaction> findByAccountAndActiveOrderByTransactionDateDesc(
            Account account, boolean active, Pageable pageable);
    
    Page<AccountTransaction> findByAccountAndActiveTrue(Account account, Pageable pageable);
    
    Page<AccountTransaction> findByAccountIdAndActiveOrderByTransactionDateDesc(
            String accountId, boolean active, Pageable pageable);
    
    Page<AccountTransaction> findAllByActiveTrue(Pageable pageable);

    // Fund transfer queries
    List<AccountTransaction> findByTransferFromAccountIdOrTransferToAccountIdAndActiveOrderByTransactionDateDesc(
            String fromAccountId, String toAccountId, boolean active);

    // Latest transaction queries
    AccountTransaction findTopByAccountAndActiveOrderByTransactionDateDesc(Account account, boolean active);
    
    AccountTransaction findTopByAccountAndActiveTrueOrderByTransactionDateDesc(Account account);
    
    AccountTransaction findTopByAccountIdAndActiveOrderByTransactionDateDesc(String accountId, boolean active);

    // Count queries
    long countByAccountAndActive(Account account, boolean active);
    
    long countByAccountIdAndActive(String accountId, boolean active);

    // Backward compatibility methods for BankAccount
    default List<AccountTransaction> findByBankAccountAndActiveTrue(Account account) {
        return findByAccountAndActiveTrue(account);
    }

    default List<AccountTransaction> findByBankAccountAndActiveTrueOrderByTransactionDateDesc(Account account) {
        return findByAccountAndActiveTrueOrderByTransactionDateDesc(account);
    }

    default List<AccountTransaction> findByBankAccountAndTransactionDateBetweenAndActiveTrue(
            Account account, LocalDateTime startDate, LocalDateTime endDate) {
        return findByAccountAndTransactionDateBetweenAndActiveTrue(account, startDate, endDate);
    }

    default Page<AccountTransaction> findByBankAccountAndActiveTrue(Account account, Pageable pageable) {
        return findByAccountAndActiveTrue(account, pageable);
    }

    default AccountTransaction findTopByBankAccountAndActiveTrueOrderByTransactionDateDesc(Account account) {
        return findTopByAccountAndActiveTrueOrderByTransactionDateDesc(account);
    }
}
