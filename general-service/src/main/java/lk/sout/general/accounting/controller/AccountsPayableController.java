package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.service.AccountsPayableService;
import lk.sout.business.trade.entity.PurchaseInvoice;
import lk.sout.business.trade.entity.Supplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/accountsPayable")
@CrossOrigin
public class AccountsPayableController {

    @Autowired
    private AccountsPayableService accountsPayableService;

    @GetMapping("/outstandingInvoices")
    public List<PurchaseInvoice> getOutstandingInvoices() {
        List<PurchaseInvoice> result = accountsPayableService.getOutstandingInvoices();
        System.out.println("AccountsPayableController: Found " + (result != null ? result.size() : 0) + " outstanding invoices");
        return result;
    }

    @GetMapping("/outstandingInvoicesBySupplier")
    public List<PurchaseInvoice> getOutstandingInvoicesBySupplier(@RequestParam String supplierId) {
        return accountsPayableService.getOutstandingInvoicesBySupplier(supplierId);
    }

    @GetMapping("/supplierOutstandingBalances")
    public Map<Supplier, Double> getSupplierOutstandingBalances() {
        return accountsPayableService.getSupplierOutstandingBalances();
    }

    @GetMapping("/agingAnalysis")
    public Map<String, List<PurchaseInvoice>> getAgingAnalysis() {
        return accountsPayableService.getAgingAnalysis();
    }

    @GetMapping("/totalAccountsPayable")
    public Double getTotalAccountsPayable() {
        return accountsPayableService.getTotalAccountsPayable();
    }

    @GetMapping("/overdueInvoices")
    public List<PurchaseInvoice> getOverdueInvoices() {
        return accountsPayableService.getOverdueInvoices();
    }

    @GetMapping("/invoicesByAgingPeriod")
    public List<PurchaseInvoice> getInvoicesByAgingPeriod(@RequestParam int days) {
        return accountsPayableService.getInvoicesByAgingPeriod(days);
    }

    @GetMapping("/supplierPaymentHistory")
    public List<PurchaseInvoice> getSupplierPaymentHistory(
            @RequestParam String supplierId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return accountsPayableService.getSupplierPaymentHistory(supplierId, startDate, endDate);
    }

    @GetMapping("/topSuppliersByOutstanding")
    public List<Map<String, Object>> getTopSuppliersByOutstanding(@RequestParam(defaultValue = "10") int limit) {
        return accountsPayableService.getTopSuppliersByOutstanding(limit);
    }

    @GetMapping("/paymentSchedule")
    public List<PurchaseInvoice> getPaymentSchedule(@RequestParam(defaultValue = "30") int daysAhead) {
        return accountsPayableService.getPaymentSchedule(daysAhead);
    }
}
