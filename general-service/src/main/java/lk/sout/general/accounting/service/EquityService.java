package lk.sout.general.accounting.service;

import lk.sout.business.accounting.entity.Equity;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface EquityService {
    
    boolean save(Equity equity);
    
    Iterable<Equity> findAll(Pageable pageable);
    
    Equity findById(String id);
    
    List<Equity> findByType(String equityType);
    
    List<Equity> findByFiscalYear(String fiscalYear);
    
    List<Equity> findByOwner(String ownerName);
    
    List<Equity> findCapitalTransactions();
    
    List<Equity> findRetainedEarningsTransactions();
    
    List<Equity> findDrawingsTransactions();
    
    List<Equity> findInvestmentTransactions();
    
    List<Equity> findWithdrawalTransactions();
    
    List<Equity> findProfitRetentionTransactions();
    
    Double getTotalEquityValue();
    
    Map<String, Double> getEquityByTypeValue();
    
    Double getRetainedEarnings();
    
    Double getTotalCapital();
    
    boolean recordProfitRetention(Double amount, String fiscalYear, String notes);
    
    boolean recordOwnerDrawing(Double amount, String ownerName, String notes);
    
    Double calculateNetWorth();
}
