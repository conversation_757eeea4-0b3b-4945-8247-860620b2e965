package lk.sout.general.accounting.service;

import lk.sout.business.accounting.entity.Account;
import lk.sout.business.accounting.entity.AccountTransaction;
import lk.sout.core.entity.Response;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

public interface AccountService {

    // Account Management
    Response save(Account account);

    Account basicSave(Account account);

    List<Account> findAllAccounts();

    List<Account> findAllActive();

    List<Account> findAllActiveAccounts();

    List<Account> findAccountsByType(String accountType);

    Account findById(String accountId);

    Account findByAccountNo(String accountNo);

    Account findByAccountCode(String accountCode);

    List<Account> findByAccountName(String accountName);

    List<Account> findByBankName(String bankName);

    List<Account> findByAccountHolderName(String accountHolderName);

    List<Account> findBySupplierName(String supplierName);

    List<Account> findByCustomerName(String customerName);

    List<Account> findByAccountType(String accountType);

    List<Account> findByAccountCategory(String accountCategory);

    Page<Account> findAllPage(int page, int pageSize);

    Page<Account> findAllAccountsPage(Pageable pageable);

    Page<Account> findAccountsByTypePage(String accountType, Pageable pageable);

    // Account Transaction Management
    Response createTransaction(AccountTransaction transaction);

    AccountTransaction basicSaveTransaction(AccountTransaction transaction);

    List<AccountTransaction> findTransactionsByAccount(String accountId);

    List<AccountTransaction> findTransactionsByReferenceNo(String referenceNo);

    List<AccountTransaction> findTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate);

    List<AccountTransaction> findTransactionsByAccountAndDateRange(
            String accountId, LocalDateTime startDate, LocalDateTime endDate);

    Page<AccountTransaction> findTransactionsPage(String accountId, int page, int pageSize);

    Page<AccountTransaction> findTransactionsPage(String accountId, Pageable pageable);

    // Balance Management
    Double getCurrentBalance(String accountId);

    boolean updateBalance(String accountId, Double amount, String operator);

    boolean debitAccount(String accountId, Double amount, String referenceNo,
                        String referenceType, String description, String thirdParty);

    boolean creditAccount(String accountId, Double amount, String referenceNo,
                         String referenceType, String description, String thirdParty);

    // Fund Transfer
    Response transferFunds(String fromAccountId, String toAccountId, Double amount,
                          String description, String referenceNo);

    // Purchase Payment Processing
    boolean processPurchasePayment(String accountId, Double amount, String purchaseInvoiceNo, String supplierName);

    // Balance Correction
    Response correctBalance(String accountId, Double newBalance, String reason, String correctedBy);

    // Utility Methods
    AccountTransaction getLastTransaction(String accountId);

    Double calculateBalanceFromTransactions(String accountId);

    boolean validateSufficientBalance(String accountId, Double amount);

    // Backward compatibility methods for legacy BankAccount references
    // These methods are kept for existing frontend code that still uses bank-specific endpoints
    default Response saveBankAccount(Account account) {
        account.setAccountType(Account.ACCOUNT_TYPE_BANK);
        return save(account);
    }

    default List<Account> findAllBankAccounts() {
        return findAccountsByType(Account.ACCOUNT_TYPE_BANK);
    }

    default Account findBankAccountById(String id) {
        return findById(id);
    }

    default List<Account> findBankAccountByBankName(String bankName) {
        return findByBankName(bankName);
    }

    default List<Account> findBankAccountByAccountHolderName(String accountHolderName) {
        return findByAccountHolderName(accountHolderName);
    }

    default List<Account> findBankAccountByAccountName(String accountName) {
        return findByAccountName(accountName);
    }

    default Page<Account> findAllBankAccountsPage(Pageable pageable) {
        return findAccountsByTypePage(Account.ACCOUNT_TYPE_BANK, pageable);
    }

    // Transaction Type Constants
    String TRANSACTION_TYPE_DEBIT = "DEBIT";
    String TRANSACTION_TYPE_CREDIT = "CREDIT";

    // Payment Type Constants
    String PAYMENT_TYPE_PURCHASE_PAYMENT = "PURCHASE_PAYMENT";
    String PAYMENT_TYPE_DEPOSIT = "DEPOSIT";
    String PAYMENT_TYPE_WITHDRAWAL = "WITHDRAWAL";
    String PAYMENT_TYPE_TRANSFER = "TRANSFER";
    String PAYMENT_TYPE_FUND_TRANSFER = "FUND_TRANSFER";

    // Reference Type Constants
    String REFERENCE_TYPE_PURCHASE_INVOICE = "PURCHASE_INVOICE";
    String REFERENCE_TYPE_SALES_INVOICE = "SALES_INVOICE";
    String REFERENCE_TYPE_DEPOSIT = "DEPOSIT";
    String REFERENCE_TYPE_WITHDRAWAL = "WITHDRAWAL";
    String REFERENCE_TYPE_FUND_TRANSFER = "FUND_TRANSFER";
    String REFERENCE_TYPE_BALANCE_CORRECTION = "BALANCE_CORRECTION";
}
