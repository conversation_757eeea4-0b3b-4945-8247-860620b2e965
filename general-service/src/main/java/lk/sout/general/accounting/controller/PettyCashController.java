package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.entity.PettyCash;
import lk.sout.business.accounting.entity.PettyCashTransaction;
import lk.sout.business.accounting.service.PettyCashService;
import lk.sout.core.entity.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/pettyCash")
@CrossOrigin
public class PettyCashController {

    @Autowired
    private PettyCashService pettyCashService;

    // Petty Cash Management Endpoints
    @PostMapping("/save")
    public Response save(@RequestBody PettyCash pettyCash) {
        return pettyCashService.save(pettyCash);
    }

    @GetMapping("/findAllActive")
    public List<PettyCash> findAllActive() {
        return pettyCashService.findAllActive();
    }

    @GetMapping("/findByPettyCashNo")
    public PettyCash findByPettyCashNo(@RequestParam String pettyCashNo) {
        return pettyCashService.findByPettyCashNo(pettyCashNo);
    }

    @GetMapping("/findById")
    public PettyCash findById(@RequestParam String id) {
        return pettyCashService.findById(id);
    }

    @GetMapping("/findByLocation")
    public List<PettyCash> findByLocation(@RequestParam String location) {
        return pettyCashService.findByLocation(location);
    }

    @GetMapping("/findByCustodian")
    public List<PettyCash> findByCustodian(@RequestParam String custodian) {
        return pettyCashService.findByCustodian(custodian);
    }

    // Transaction Management Endpoints
    @PostMapping("/createTransaction")
    public Response createTransaction(@RequestBody PettyCashTransaction transaction) {
        return pettyCashService.createTransaction(transaction);
    }

    @PostMapping("/replenish")
    public Response replenishPettyCash(@RequestParam String pettyCashId,
                                      @RequestParam Double amount,
                                      @RequestParam String description,
                                      @RequestParam(required = false) String voucherNo) {
        return pettyCashService.replenishPettyCash(pettyCashId, amount, description, voucherNo);
    }

    @PostMapping("/recordExpense")
    public Response recordExpense(@RequestParam String pettyCashId,
                                 @RequestParam Double amount,
                                 @RequestParam String description,
                                 @RequestParam String expenseCategoryId,
                                 @RequestParam(required = false) String voucherNo,
                                 @RequestParam(required = false) String receivedBy) {
        return pettyCashService.recordExpense(pettyCashId, amount, description, expenseCategoryId, voucherNo, receivedBy);
    }

    @PostMapping("/recordRefund")
    public Response recordRefund(@RequestParam String pettyCashId,
                                @RequestParam Double amount,
                                @RequestParam String description,
                                @RequestParam(required = false) String voucherNo) {
        return pettyCashService.recordRefund(pettyCashId, amount, description, voucherNo);
    }

    // Transaction Query Endpoints
    @GetMapping("/findTransactionsByPettyCash")
    public List<PettyCashTransaction> findTransactionsByPettyCash(@RequestParam String pettyCashId) {
        return pettyCashService.findTransactionsByPettyCash(pettyCashId);
    }

    @GetMapping("/findTransactionsByPettyCashPaged")
    public List<PettyCashTransaction> findTransactionsByPettyCash(@RequestParam String pettyCashId,
                                                                 @RequestParam(defaultValue = "0") int page,
                                                                 @RequestParam(defaultValue = "10") int pageSize) {
        Pageable pageable = PageRequest.of(page, pageSize);
        return pettyCashService.findTransactionsByPettyCash(pettyCashId, pageable);
    }

    @GetMapping("/findTransactionsByDateRange")
    public List<PettyCashTransaction> findTransactionsByDateRange(
            @RequestParam String pettyCashId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return pettyCashService.findTransactionsByDateRange(pettyCashId, startDate, endDate);
    }

    @GetMapping("/findTransactionsByType")
    public List<PettyCashTransaction> findTransactionsByType(@RequestParam String pettyCashId,
                                                            @RequestParam String transactionTypeId) {
        return pettyCashService.findTransactionsByType(pettyCashId, transactionTypeId);
    }

    @GetMapping("/findTransactionsByExpenseCategory")
    public List<PettyCashTransaction> findTransactionsByExpenseCategory(@RequestParam String pettyCashId,
                                                                       @RequestParam String expenseCategoryId) {
        return pettyCashService.findTransactionsByExpenseCategory(pettyCashId, expenseCategoryId);
    }

    @GetMapping("/findTransactionsByOperator")
    public List<PettyCashTransaction> findTransactionsByOperator(@RequestParam String pettyCashId,
                                                                @RequestParam String operator) {
        return pettyCashService.findTransactionsByOperator(pettyCashId, operator);
    }

    @GetMapping("/findAllTransactionsByDateRange")
    public List<PettyCashTransaction> findAllTransactionsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return pettyCashService.findAllTransactionsByDateRange(startDate, endDate);
    }

    @GetMapping("/findTransactionByVoucherNo")
    public PettyCashTransaction findTransactionByVoucherNo(@RequestParam String voucherNo) {
        return pettyCashService.findTransactionByVoucherNo(voucherNo);
    }

    // Balance Management Endpoints
    @GetMapping("/getCurrentBalance")
    public Double getCurrentBalance(@RequestParam String pettyCashId) {
        return pettyCashService.getCurrentBalance(pettyCashId);
    }

    @PostMapping("/updateBalance")
    public boolean updateBalance(@RequestParam String pettyCashId,
                                @RequestParam Double amount,
                                @RequestParam String operator) {
        return pettyCashService.updateBalance(pettyCashId, amount, operator);
    }

    @GetMapping("/validateSufficientBalance")
    public boolean validateSufficientBalance(@RequestParam String pettyCashId,
                                           @RequestParam Double amount) {
        return pettyCashService.validateSufficientBalance(pettyCashId, amount);
    }
}
