package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.service.AccountsReceivableService;
import lk.sout.business.trade.entity.Customer;
import lk.sout.business.trade.entity.SalesInvoice;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/accountsReceivable")
@CrossOrigin
public class AccountsReceivableController {

    @Autowired
    private AccountsReceivableService accountsReceivableService;

    @GetMapping("/outstandingInvoices")
    public List<SalesInvoice> getOutstandingInvoices() {
        List<SalesInvoice> result = accountsReceivableService.getOutstandingInvoices();
        System.out.println("AccountsReceivableController: Found " + (result != null ? result.size() : 0) + " outstanding invoices");
        return result;
    }

    @GetMapping("/outstandingInvoicesByCustomer")
    public List<SalesInvoice> getOutstandingInvoicesByCustomer(@RequestParam String customerId) {
        return accountsReceivableService.getOutstandingInvoicesByCustomer(customerId);
    }

    @GetMapping("/customerOutstandingBalances")
    public Map<Customer, Double> getCustomerOutstandingBalances() {
        return accountsReceivableService.getCustomerOutstandingBalances();
    }

    @GetMapping("/agingAnalysis")
    public Map<String, List<SalesInvoice>> getAgingAnalysis() {
        return accountsReceivableService.getAgingAnalysis();
    }

    @GetMapping("/totalAccountsReceivable")
    public Double getTotalAccountsReceivable() {
        return accountsReceivableService.getTotalAccountsReceivable();
    }

    @GetMapping("/overdueInvoices")
    public List<SalesInvoice> getOverdueInvoices() {
        return accountsReceivableService.getOverdueInvoices();
    }

    @GetMapping("/invoicesByAgingPeriod")
    public List<SalesInvoice> getInvoicesByAgingPeriod(@RequestParam int days) {
        return accountsReceivableService.getInvoicesByAgingPeriod(days);
    }

    @GetMapping("/customerPaymentHistory")
    public List<SalesInvoice> getCustomerPaymentHistory(
            @RequestParam String customerId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        return accountsReceivableService.getCustomerPaymentHistory(customerId, startDate, endDate);
    }

    @GetMapping("/topCustomersByOutstanding")
    public List<Map<String, Object>> getTopCustomersByOutstanding(@RequestParam(defaultValue = "10") int limit) {
        return accountsReceivableService.getTopCustomersByOutstanding(limit);
    }
}
