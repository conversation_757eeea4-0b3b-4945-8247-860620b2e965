package lk.sout.general.accounting.service.impl;

import lk.sout.business.accounting.entity.Account;
import lk.sout.business.accounting.entity.AccountTransaction;
import lk.sout.business.accounting.repository.AccountRepository;
import lk.sout.business.accounting.repository.AccountTransactionRepository;
import lk.sout.business.accounting.service.AccountService;
import lk.sout.core.entity.Action;
import lk.sout.core.entity.MetaData;
import lk.sout.core.entity.Response;
import lk.sout.core.service.ActionService;
import lk.sout.core.service.MetaDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class AccountServiceImpl implements AccountService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountServiceImpl.class);

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private AccountTransactionRepository accountTransactionRepository;

    @Autowired
    private ActionService actionService;

    @Autowired
    private MetaDataService metaDataService;

    // Account Management

    @Override
    @Transactional
    public Response save(Account account) {
        Response response = new Response();
        try {
            // Set audit fields
            if (account.getId() == null) {
                account.setCreatedDate(LocalDateTime.now());
                
                // Generate account code if not provided
                if (account.getAccountCode() == null || account.getAccountCode().isEmpty()) {
                    account.setAccountCode(generateAccountCode(account.getAccountType()));
                }
                
                // Generate account number if not provided
                if (account.getAccountNo() == null || account.getAccountNo().isEmpty()) {
                    account.setAccountNo(generateAccountNumber());
                }
            }
            account.setLastModifiedDate(LocalDateTime.now());

            Account savedAccount = accountRepository.save(account);

            // Create opening balance transaction if opening balance > 0
            if (savedAccount.getOpeningBalance() != null && savedAccount.getOpeningBalance() > 0) {
                AccountTransaction openingTransaction = new AccountTransaction();
                openingTransaction.setAccount(savedAccount);
                openingTransaction.setTransactionType(AccountTransaction.TRANSACTION_TYPE_CREDIT);
                openingTransaction.setPaymentType(AccountTransaction.PAYMENT_TYPE_DEPOSIT);
                openingTransaction.setAmount(savedAccount.getOpeningBalance());
                openingTransaction.setReferenceNo("OPENING_BALANCE");
                openingTransaction.setReferenceType("OPENING_BALANCE");
                openingTransaction.setDescription("Opening balance for account: " + savedAccount.getAccountName());
                openingTransaction.setBalanceAfterTransaction(savedAccount.getOpeningBalance());
                openingTransaction.setTransactionDate(LocalDateTime.now());
                openingTransaction.setCreatedDate(LocalDateTime.now());
                openingTransaction.setLastModifiedDate(LocalDateTime.now());

                accountTransactionRepository.save(openingTransaction);

                // Update account current balance
                savedAccount.setCurrentBalance(savedAccount.getOpeningBalance());
                accountRepository.save(savedAccount);
            }

            // Log action
            Action action = new Action();
            action.setType("Account Management");
            action.setRemark("Account saved: " + savedAccount.getAccountName());
            action.setReference(savedAccount.getId());
            actionService.save(action);

            response.setCode(200);
            response.setMessage("Account saved successfully");
            response.setSuccess(true);
            response.setData(savedAccount.getId());
        } catch (Exception e) {
            LOGGER.error("Error saving account: ", e);
            response.setCode(500);
            response.setSuccess(false);
            response.setMessage("Error saving account: " + e.getMessage());
        }
        return response;
    }

    @Override
    public Account basicSave(Account account) {
        try {
            return accountRepository.save(account);
        } catch (Exception e) {
            LOGGER.error("Error in basic save: " + e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<Account> findAllAccounts() {
        return accountRepository.findAll();
    }

    @Override
    public List<Account> findAllActive() {
        return accountRepository.findAllByActiveTrue();
    }

    @Override
    public List<Account> findAllActiveAccounts() {
        return accountRepository.findAllByActiveOrderByAccountNameAsc(true);
    }

    @Override
    public List<Account> findAccountsByType(String accountType) {
        return accountRepository.findAllByAccountTypeAndActiveOrderByAccountNameAsc(accountType, true);
    }

    @Override
    public Account findById(String accountId) {
        return accountRepository.findByIdAndActiveTrue(accountId);
    }

    @Override
    public Account findByAccountNo(String accountNo) {
        return accountRepository.findByAccountNo(accountNo);
    }

    @Override
    public Account findByAccountCode(String accountCode) {
        return accountRepository.findByAccountCode(accountCode);
    }

    @Override
    public List<Account> findByAccountName(String accountName) {
        return accountRepository.findByAccountNameContainingIgnoreCaseAndActive(accountName, true);
    }

    @Override
    public List<Account> findByBankName(String bankName) {
        return accountRepository.findByBankNameContainingIgnoreCaseAndActive(bankName, true);
    }

    @Override
    public List<Account> findByAccountHolderName(String accountHolderName) {
        return accountRepository.findByAccountHolderNameContainingIgnoreCaseAndActive(accountHolderName, true);
    }

    @Override
    public List<Account> findBySupplierName(String supplierName) {
        return accountRepository.findBySupplierNameContainingIgnoreCaseAndActive(supplierName, true);
    }

    @Override
    public List<Account> findByCustomerName(String customerName) {
        return accountRepository.findByCustomerNameContainingIgnoreCaseAndActive(customerName, true);
    }

    @Override
    public List<Account> findByAccountType(String accountType) {
        return accountRepository.findByAccountTypeAndActiveTrue(accountType);
    }

    @Override
    public List<Account> findByAccountCategory(String accountCategory) {
        return accountRepository.findByAccountCategoryAndActiveTrue(accountCategory);
    }

    @Override
    public Page<Account> findAllPage(int page, int pageSize) {
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by("accountName").ascending());
        return accountRepository.findAllByActiveTrue(pageable);
    }

    @Override
    public Page<Account> findAllAccountsPage(Pageable pageable) {
        return accountRepository.findAllByActiveOrderByAccountNameAsc(true, pageable);
    }

    @Override
    public Page<Account> findAccountsByTypePage(String accountType, Pageable pageable) {
        return accountRepository.findAllByAccountTypeAndActiveOrderByAccountNameAsc(accountType, true, pageable);
    }

    // Helper methods for code generation
    private String generateAccountCode(String accountType) {
        try {
            MetaData metaData = metaDataService.searchMetaData("ACCOUNT_CODE_SEQUENCE", "Sequence");
            if (metaData == null) {
                metaData = new MetaData();
                metaData.setName("ACCOUNT_CODE_SEQUENCE");
                metaData.setValue("1000");
                metaDataService.save(metaData);
            }
            
            int nextCode = Integer.parseInt(metaData.getValue()) + 1;
            metaData.setValue(String.valueOf(nextCode));
            metaDataService.save(metaData);
            
            String prefix = getAccountTypePrefix(accountType);
            return prefix + String.format("%04d", nextCode);
        } catch (Exception e) {
            LOGGER.error("Error generating account code: " + e.getMessage(), e);
            return "ACC" + System.currentTimeMillis();
        }
    }

    private String generateAccountNumber() {
        try {
            MetaData metaData = metaDataService.searchMetaData("ACCOUNT_NUMBER_SEQUENCE", "Sequence");
            if (metaData == null) {
                metaData = new MetaData();
                metaData.setName("ACCOUNT_NUMBER_SEQUENCE");
                metaData.setValue("100000");
                metaDataService.save(metaData);
            }
            
            int nextNumber = Integer.parseInt(metaData.getValue()) + 1;
            metaData.setValue(String.valueOf(nextNumber));
            metaDataService.save(metaData);
            
            return String.valueOf(nextNumber);
        } catch (Exception e) {
            LOGGER.error("Error generating account number: " + e.getMessage(), e);
            return String.valueOf(System.currentTimeMillis());
        }
    }

    private String getAccountTypePrefix(String accountType) {
        if (accountType == null) return "ACC";

        switch (accountType) {
            case Account.ACCOUNT_TYPE_BANK:
                return "BNK";
            case Account.ACCOUNT_TYPE_CASH:
                return "CSH";
            case Account.ACCOUNT_TYPE_SUPPLIER:
                return "SUP";
            case Account.ACCOUNT_TYPE_CUSTOMER:
                return "CUS";
            case Account.ACCOUNT_TYPE_PETTY_CASH:
                return "PTY";
            default:
                return "ACC";
        }
    }

    // Account Transaction Management

    @Override
    @Transactional
    public Response createTransaction(AccountTransaction transaction) {
        Response response = new Response();
        try {
            Account account = findById(transaction.getAccount().getId());
            if (account == null) {
                response.setSuccess(false);
                response.setMessage("Account not found");
                return response;
            }

            transaction.setTransactionDate(LocalDateTime.now());
            transaction.setActive(true);

            // Calculate balance after transaction
            Double currentBalance = account.getCurrentBalance() != null ? account.getCurrentBalance() : 0.0;
            Double newBalance;

            if (AccountTransaction.TRANSACTION_TYPE_CREDIT.equals(transaction.getTransactionType())) {
                newBalance = currentBalance + transaction.getAmount();
            } else {
                newBalance = currentBalance - transaction.getAmount();
            }

            transaction.setBalanceAfterTransaction(newBalance);
            AccountTransaction savedTransaction = accountTransactionRepository.save(transaction);

            // Update account balance
            account.setCurrentBalance(newBalance);
            account.setLastModifiedDate(LocalDateTime.now());
            accountRepository.save(account);

            response.setSuccess(true);
            response.setMessage("Transaction created successfully");
            response.setData(savedTransaction.getId());
        } catch (Exception e) {
            LOGGER.error("Error creating transaction: " + e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error creating transaction: " + e.getMessage());
        }
        return response;
    }

    @Override
    public AccountTransaction basicSaveTransaction(AccountTransaction transaction) {
        return accountTransactionRepository.save(transaction);
    }

    @Override
    public List<AccountTransaction> findTransactionsByAccount(String accountId) {
        Account account = findById(accountId);
        if (account != null) {
            return accountTransactionRepository.findByAccountAndActiveTrueOrderByTransactionDateDesc(account);
        }
        return List.of();
    }

    @Override
    public List<AccountTransaction> findTransactionsByReferenceNo(String referenceNo) {
        return accountTransactionRepository.findByReferenceNoAndActive(referenceNo, true);
    }

    @Override
    public List<AccountTransaction> findTransactionsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return accountTransactionRepository.findByTransactionDateBetweenAndActiveOrderByTransactionDateDesc(
                startDate, endDate, true);
    }

    @Override
    public List<AccountTransaction> findTransactionsByAccountAndDateRange(
            String accountId, LocalDateTime startDate, LocalDateTime endDate) {
        Account account = findById(accountId);
        if (account != null) {
            return accountTransactionRepository.findByAccountAndTransactionDateBetweenAndActiveOrderByTransactionDateDesc(
                    account, startDate, endDate, true);
        }
        return List.of();
    }

    @Override
    public Page<AccountTransaction> findTransactionsPage(String accountId, int page, int pageSize) {
        Account account = findById(accountId);
        if (account != null) {
            Pageable pageable = PageRequest.of(page, pageSize, Sort.by("transactionDate").descending());
            return accountTransactionRepository.findByAccountAndActiveTrue(account, pageable);
        }
        return Page.empty();
    }

    @Override
    public Page<AccountTransaction> findTransactionsPage(String accountId, Pageable pageable) {
        Account account = findById(accountId);
        if (account != null) {
            return accountTransactionRepository.findByAccountAndActiveOrderByTransactionDateDesc(account, true, pageable);
        }
        return Page.empty();
    }

    // Balance Management

    @Override
    public Double getCurrentBalance(String accountId) {
        Account account = findById(accountId);
        return account != null ? account.getCurrentBalance() : 0.0;
    }

    @Override
    @Transactional
    public boolean updateBalance(String accountId, Double amount, String operator) {
        try {
            Account account = findById(accountId);
            if (account == null) {
                return false;
            }

            Double currentBalance = account.getCurrentBalance();
            Double newBalance;

            if ("+".equals(operator)) {
                newBalance = currentBalance + amount;
            } else if ("-".equals(operator)) {
                newBalance = currentBalance - amount;
            } else {
                return false;
            }

            account.setCurrentBalance(newBalance);
            account.setLastModifiedDate(LocalDateTime.now());
            accountRepository.save(account);
            return true;
        } catch (Exception e) {
            LOGGER.error("Error updating balance: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean debitAccount(String accountId, Double amount, String referenceNo,
                               String referenceType, String description, String thirdParty) {
        try {
            Account account = findById(accountId);
            if (account == null) {
                return false;
            }

            // Check sufficient balance for debit
            if (!validateSufficientBalance(accountId, amount)) {
                LOGGER.warn("Insufficient balance for debit. Account: {}, Amount: {}", accountId, amount);
                return false;
            }

            AccountTransaction transaction = new AccountTransaction();
            transaction.setAccount(account);
            transaction.setTransactionType(AccountTransaction.TRANSACTION_TYPE_DEBIT);
            transaction.setAmount(amount);
            transaction.setReferenceNo(referenceNo);
            transaction.setReferenceType(referenceType);
            transaction.setDescription(description);
            transaction.setThirdParty(thirdParty);

            Response response = createTransaction(transaction);
            return response.isSuccess();
        } catch (Exception e) {
            LOGGER.error("Error debiting account: " + e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public boolean creditAccount(String accountId, Double amount, String referenceNo,
                                String referenceType, String description, String thirdParty) {
        try {
            Account account = findById(accountId);
            if (account == null) {
                return false;
            }

            AccountTransaction transaction = new AccountTransaction();
            transaction.setAccount(account);
            transaction.setTransactionType(AccountTransaction.TRANSACTION_TYPE_CREDIT);
            transaction.setAmount(amount);
            transaction.setReferenceNo(referenceNo);
            transaction.setReferenceType(referenceType);
            transaction.setDescription(description);
            transaction.setThirdParty(thirdParty);

            Response response = createTransaction(transaction);
            return response.isSuccess();
        } catch (Exception e) {
            LOGGER.error("Error crediting account: " + e.getMessage(), e);
            return false;
        }
    }

    // Fund Transfer

    @Override
    @Transactional
    public Response transferFunds(String fromAccountId, String toAccountId, Double amount,
                                 String description, String referenceNo) {
        Response response = new Response();
        try {
            Account fromAccount = findById(fromAccountId);
            Account toAccount = findById(toAccountId);

            if (fromAccount == null || toAccount == null) {
                response.setSuccess(false);
                response.setMessage("One or both accounts not found");
                return response;
            }

            if (!validateSufficientBalance(fromAccountId, amount)) {
                response.setSuccess(false);
                response.setMessage("Insufficient balance in source account");
                return response;
            }

            // Debit from source account
            AccountTransaction debitTransaction = new AccountTransaction();
            debitTransaction.setAccount(fromAccount);
            debitTransaction.setTransactionType(AccountTransaction.TRANSACTION_TYPE_DEBIT);
            debitTransaction.setPaymentType(AccountTransaction.PAYMENT_TYPE_FUND_TRANSFER);
            debitTransaction.setAmount(amount);
            debitTransaction.setReferenceNo(referenceNo);
            debitTransaction.setReferenceType(AccountTransaction.REFERENCE_TYPE_FUND_TRANSFER);
            debitTransaction.setDescription("Fund transfer to " + toAccount.getAccountName() + " - " + description);
            debitTransaction.setTransferToAccountId(toAccountId);
            debitTransaction.setTransferToAccount(toAccount);

            // Credit to destination account
            AccountTransaction creditTransaction = new AccountTransaction();
            creditTransaction.setAccount(toAccount);
            creditTransaction.setTransactionType(AccountTransaction.TRANSACTION_TYPE_CREDIT);
            creditTransaction.setPaymentType(AccountTransaction.PAYMENT_TYPE_FUND_TRANSFER);
            creditTransaction.setAmount(amount);
            creditTransaction.setReferenceNo(referenceNo);
            creditTransaction.setReferenceType(AccountTransaction.REFERENCE_TYPE_FUND_TRANSFER);
            creditTransaction.setDescription("Fund transfer from " + fromAccount.getAccountName() + " - " + description);
            creditTransaction.setTransferFromAccountId(fromAccountId);

            Response debitResponse = createTransaction(debitTransaction);
            Response creditResponse = createTransaction(creditTransaction);

            if (debitResponse.isSuccess() && creditResponse.isSuccess()) {
                // Log action
                Action action = new Action();
                action.setType("Fund Transfer");
                action.setRemark("Fund transfer from " + fromAccount.getAccountName() +
                                    " to " + toAccount.getAccountName() + " - Amount: " + amount);
                action.setReference(referenceNo);
                actionService.save(action);

                response.setSuccess(true);
                response.setMessage("Fund transfer completed successfully");
            } else {
                response.setSuccess(false);
                response.setMessage("Fund transfer failed");
            }
        } catch (Exception e) {
            LOGGER.error("Error in fund transfer: " + e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error in fund transfer: " + e.getMessage());
        }
        return response;
    }

    // Purchase Payment Processing

    @Override
    @Transactional
    public boolean processPurchasePayment(String accountId, Double amount, String purchaseInvoiceNo, String supplierName) {
        return debitAccount(accountId, amount, purchaseInvoiceNo,
            AccountTransaction.REFERENCE_TYPE_PURCHASE_INVOICE,
            "Purchase payment to " + supplierName, supplierName);
    }

    // Balance Correction

    @Override
    @Transactional
    public Response correctBalance(String accountId, Double newBalance, String reason, String correctedBy) {
        Response response = new Response();
        try {
            Account account = findById(accountId);
            if (account == null) {
                response.setSuccess(false);
                response.setMessage("Account not found");
                return response;
            }

            Double currentBalance = account.getCurrentBalance();
            Double difference = newBalance - currentBalance;

            if (difference != 0) {
                AccountTransaction correctionTransaction = new AccountTransaction();
                correctionTransaction.setAccount(account);
                correctionTransaction.setTransactionType(
                    difference > 0 ? AccountTransaction.TRANSACTION_TYPE_CREDIT : AccountTransaction.TRANSACTION_TYPE_DEBIT);
                correctionTransaction.setPaymentType("BALANCE_CORRECTION");
                correctionTransaction.setAmount(Math.abs(difference));
                correctionTransaction.setReferenceNo("BALANCE_CORRECTION_" + System.currentTimeMillis());
                correctionTransaction.setReferenceType(AccountTransaction.REFERENCE_TYPE_BALANCE_CORRECTION);
                correctionTransaction.setDescription("Balance correction: " + reason);
                correctionTransaction.setThirdParty(correctedBy);

                Response transactionResponse = createTransaction(correctionTransaction);
                if (transactionResponse.isSuccess()) {
                    // Log action
                    Action action = new Action();
                    action.setType("Bank Balance Changes");
                    action.setRemark("Balance corrected for account " + account.getAccountName() +
                                        " from " + currentBalance + " to " + newBalance + ". Reason: " + reason);
                    action.setReference(account.getId());
                    actionService.save(action);

                    response.setSuccess(true);
                    response.setMessage("Balance corrected successfully");
                } else {
                    response.setSuccess(false);
                    response.setMessage("Failed to create correction transaction");
                }
            } else {
                response.setSuccess(true);
                response.setMessage("No correction needed - balance is already correct");
            }
        } catch (Exception e) {
            LOGGER.error("Error correcting balance: " + e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("Error correcting balance: " + e.getMessage());
        }
        return response;
    }

    // Utility Methods

    @Override
    public AccountTransaction getLastTransaction(String accountId) {
        Account account = findById(accountId);
        if (account != null) {
            return accountTransactionRepository.findTopByAccountAndActiveOrderByTransactionDateDesc(account, true);
        }
        return null;
    }

    @Override
    public Double calculateBalanceFromTransactions(String accountId) {
        Account account = findById(accountId);
        if (account == null) {
            return 0.0;
        }

        List<AccountTransaction> transactions = accountTransactionRepository.findByAccountAndActiveTrueOrderByTransactionDateDesc(account);
        Double balance = account.getOpeningBalance() != null ? account.getOpeningBalance() : 0.0;

        for (AccountTransaction transaction : transactions) {
            if (AccountTransaction.TRANSACTION_TYPE_CREDIT.equals(transaction.getTransactionType())) {
                balance += transaction.getAmount();
            } else {
                balance -= transaction.getAmount();
            }
        }

        return balance;
    }

    @Override
    public boolean validateSufficientBalance(String accountId, Double amount) {
        try {
            Double currentBalance = getCurrentBalance(accountId);
            return currentBalance != null && currentBalance >= amount;
        } catch (Exception e) {
            LOGGER.error("Error validating balance: " + e.getMessage(), e);
            return false;
        }
    }
}
