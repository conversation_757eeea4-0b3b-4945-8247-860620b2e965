package lk.sout.general.accounting.repository;

import lk.sout.business.accounting.entity.Liability;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;

public interface LiabilityRepository extends MongoRepository<Liability, String> {
    
    Page<Liability> findByActiveTrue(Pageable pageable);
    
    List<Liability> findByLiabilityTypeAndActiveTrue(String liabilityType);
    
    List<Liability> findByStatusAndActiveTrue(String status);
    
    List<Liability> findByCategoryAndActiveTrue(String category);
    
    @Query("{'liabilityType': 'CURRENT', 'active': true}")
    List<Liability> findCurrentLiabilities();
    
    @Query("{'liabilityType': 'LONG_TERM', 'active': true}")
    List<Liability> findLongTermLiabilities();
    
    @Query("{'status': 'ACTIVE', 'active': true}")
    List<Liability> findActiveLiabilities();
    
    @Query("{'status': 'OVERDUE', 'active': true}")
    List<Liability> findOverdueLiabilities();
    
    @Query("{'dueDate': {$lt: ?0}, 'status': 'ACTIVE', 'active': true}")
    List<Liability> findOverdueLiabilitiesByDate(Date currentDate);
    
    @Query("{'nextPaymentDate': {$gte: ?0, $lte: ?1}, 'active': true}")
    List<Liability> findUpcomingPayments(Date startDate, Date endDate);
    
    @Query("{'active': true}")
    List<Liability> findAllActiveLiabilities();
}
