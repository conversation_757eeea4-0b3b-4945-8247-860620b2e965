package lk.sout.general.accounting.repository;

import lk.sout.business.accounting.entity.Equity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface EquityRepository extends MongoRepository<Equity, String> {
    
    Page<Equity> findByActiveTrue(Pageable pageable);
    
    List<Equity> findByEquityTypeAndActiveTrue(String equityType);
    
    List<Equity> findByFiscalYearAndActiveTrue(String fiscalYear);
    
    List<Equity> findByOwnerNameAndActiveTrue(String ownerName);
    
    List<Equity> findByTransactionTypeAndActiveTrue(String transactionType);
    
    @Query("{'equityType': 'CAPITAL', 'active': true}")
    List<Equity> findCapitalTransactions();
    
    @Query("{'equityType': 'RETAINED_EARNINGS', 'active': true}")
    List<Equity> findRetainedEarningsTransactions();
    
    @Query("{'equityType': 'DRAWINGS', 'active': true}")
    List<Equity> findDrawingsTransactions();
    
    @Query("{'transactionType': 'INVESTMENT', 'active': true}")
    List<Equity> findInvestmentTransactions();
    
    @Query("{'transactionType': 'WITHDRAWAL', 'active': true}")
    List<Equity> findWithdrawalTransactions();
    
    @Query("{'transactionType': 'PROFIT_RETENTION', 'active': true}")
    List<Equity> findProfitRetentionTransactions();
    
    @Query("{'active': true}")
    List<Equity> findAllActiveEquity();
}
