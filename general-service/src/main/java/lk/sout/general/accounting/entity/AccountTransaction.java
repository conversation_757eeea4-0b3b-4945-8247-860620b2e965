package lk.sout.general.accounting.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Unified AccountTransaction entity for tracking all account transactions
 * Combines functionality from both core and accounting packages
 */
@Document
@Component
public class AccountTransaction {

    @Id
    private String id;

    @DBRef
    private Account account;

    private String transactionType; // DEBIT, CREDIT

    private String paymentType; // PURCHASE_PAYMENT, DEPOSIT, WITHDRAWAL, TRANSFER, FUND_TRANSFER

    private Double amount;

    private String referenceNo; // Purchase Invoice No, Deposit Slip No, etc.

    private String referenceType; // PURCHASE_INVOICE, DEPOSIT, WITHDRAWAL, FUND_TRANSFER

    private String description;

    private String thirdParty; // Supplier name, customer name, etc.

    private LocalDateTime transactionDate;

    private Double balanceAfterTransaction;

    // For fund transfers
    @DBRef
    private Account transferToAccount;

    private String transferFromAccountId;

    private String transferToAccountId;

    private boolean active;

    // Audit fields
    private String createdBy;

    private LocalDateTime createdDate;

    private String lastModifiedBy;

    private LocalDateTime lastModifiedDate;

    // Constructors
    public AccountTransaction() {
        this.active = true;
        this.transactionDate = LocalDateTime.now();
        this.createdDate = LocalDateTime.now();
        this.lastModifiedDate = LocalDateTime.now();
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getReferenceNo() {
        return referenceNo;
    }

    public void setReferenceNo(String referenceNo) {
        this.referenceNo = referenceNo;
    }

    public String getReferenceType() {
        return referenceType;
    }

    public void setReferenceType(String referenceType) {
        this.referenceType = referenceType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getThirdParty() {
        return thirdParty;
    }

    public void setThirdParty(String thirdParty) {
        this.thirdParty = thirdParty;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public Double getBalanceAfterTransaction() {
        return balanceAfterTransaction;
    }

    public void setBalanceAfterTransaction(Double balanceAfterTransaction) {
        this.balanceAfterTransaction = balanceAfterTransaction;
    }

    public Account getTransferToAccount() {
        return transferToAccount;
    }

    public void setTransferToAccount(Account transferToAccount) {
        this.transferToAccount = transferToAccount;
    }

    public String getTransferFromAccountId() {
        return transferFromAccountId;
    }

    public void setTransferFromAccountId(String transferFromAccountId) {
        this.transferFromAccountId = transferFromAccountId;
    }

    public String getTransferToAccountId() {
        return transferToAccountId;
    }

    public void setTransferToAccountId(String transferToAccountId) {
        this.transferToAccountId = transferToAccountId;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    // Transaction Type Constants
    public static final String TRANSACTION_TYPE_DEBIT = "DEBIT";
    public static final String TRANSACTION_TYPE_CREDIT = "CREDIT";

    // Payment Type Constants
    public static final String PAYMENT_TYPE_PURCHASE_PAYMENT = "PURCHASE_PAYMENT";
    public static final String PAYMENT_TYPE_DEPOSIT = "DEPOSIT";
    public static final String PAYMENT_TYPE_WITHDRAWAL = "WITHDRAWAL";
    public static final String PAYMENT_TYPE_TRANSFER = "TRANSFER";
    public static final String PAYMENT_TYPE_FUND_TRANSFER = "FUND_TRANSFER";

    // Reference Type Constants
    public static final String REFERENCE_TYPE_PURCHASE_INVOICE = "PURCHASE_INVOICE";
    public static final String REFERENCE_TYPE_SALES_INVOICE = "SALES_INVOICE";
    public static final String REFERENCE_TYPE_DEPOSIT = "DEPOSIT";
    public static final String REFERENCE_TYPE_WITHDRAWAL = "WITHDRAWAL";
    public static final String REFERENCE_TYPE_FUND_TRANSFER = "FUND_TRANSFER";
    public static final String REFERENCE_TYPE_BALANCE_CORRECTION = "BALANCE_CORRECTION";
}
