package lk.sout.general.accounting.repository;

import lk.sout.business.accounting.entity.Account;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AccountRepository extends MongoRepository<Account, String> {

    // Basic queries
    List<Account> findAllByActiveTrue();
    
    List<Account> findAllByActiveOrderByAccountNameAsc(boolean active);

    Account findByIdAndActiveTrue(String id);

    // Account type queries
    List<Account> findAllByAccountTypeAndActiveOrderByAccountNameAsc(String accountType, boolean active);
    
    List<Account> findByAccountTypeAndActiveTrue(String accountType);

    // Account number and code queries
    Account findByAccountNo(String accountNo);
    
    Account findByAccountNoAndActive(String accountNo, boolean active);
    
    Account findByAccountCode(String accountCode);

    // Search queries
    List<Account> findByAccountNameContainingIgnoreCaseAndActive(String accountName, boolean active);
    
    List<Account> findByAccountNameContainingIgnoreCaseAndActiveTrue(String accountName);

    // Bank specific queries
    List<Account> findByBankNameContainingIgnoreCaseAndActive(String bankName, boolean active);
    
    List<Account> findByBankNameContainingIgnoreCaseAndActiveTrue(String bankName);
    
    List<Account> findByAccountHolderNameContainingIgnoreCaseAndActive(String accountHolderName, boolean active);
    
    List<Account> findByAccountHolderNameContainingIgnoreCaseAndActiveTrue(String accountHolderName);

    // Supplier/Customer queries
    List<Account> findBySupplierIdAndActive(String supplierId, boolean active);
    
    List<Account> findByCustomerIdAndActive(String customerId, boolean active);
    
    List<Account> findBySupplierNameContainingIgnoreCaseAndActive(String supplierName, boolean active);
    
    List<Account> findByCustomerNameContainingIgnoreCaseAndActive(String customerName, boolean active);

    // Account category queries
    List<Account> findByAccountCategoryAndActiveTrue(String accountCategory);

    // Hierarchical queries
    List<Account> findByParentAccountIdAndActiveTrue(String parentAccountId);

    // Pagination queries
    Page<Account> findAllByActiveTrue(Pageable pageable);
    
    Page<Account> findAllByActiveOrderByAccountNameAsc(boolean active, Pageable pageable);
    
    Page<Account> findAllByAccountTypeAndActiveOrderByAccountNameAsc(String accountType, boolean active, Pageable pageable);

    // Count queries
    long countByActiveAndAccountType(boolean active, String accountType);
    
    long countByActive(boolean active);

    // Backward compatibility methods for legacy BankAccount references
    default List<Account> findAllBankAccountsByActiveTrue() {
        return findByAccountTypeAndActiveTrue(Account.ACCOUNT_TYPE_BANK);
    }

    default Account findBankAccountByIdAndActiveTrue(String id) {
        return findByIdAndActiveTrue(id);
    }

    default List<Account> findBankAccountByBankNameContainingIgnoreCaseAndActiveTrue(String bankName) {
        return findByBankNameContainingIgnoreCaseAndActiveTrue(bankName);
    }

    default List<Account> findBankAccountByAccountHolderNameContainingIgnoreCaseAndActiveTrue(String accountHolderName) {
        return findByAccountHolderNameContainingIgnoreCaseAndActiveTrue(accountHolderName);
    }

    default List<Account> findBankAccountByAccountNameContainingIgnoreCaseAndActiveTrue(String accountName) {
        return findByAccountNameContainingIgnoreCaseAndActiveTrue(accountName);
    }

    default Page<Account> findAllBankAccountsByActiveTrue(Pageable pageable) {
        return findAllByAccountTypeAndActiveOrderByAccountNameAsc(Account.ACCOUNT_TYPE_BANK, true, pageable);
    }
}
