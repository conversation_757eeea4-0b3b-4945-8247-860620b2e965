package lk.sout.general.accounting.service;

import lk.sout.business.trade.entity.Customer;
import lk.sout.business.trade.entity.SalesInvoice;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface AccountsReceivableService {

    /**
     * Get all outstanding sales invoices (balance > 0)
     */
    List<SalesInvoice> getOutstandingInvoices();

    /**
     * Get outstanding invoices by customer
     */
    List<SalesInvoice> getOutstandingInvoicesByCustomer(String customerId);

    /**
     * Get customer-wise outstanding balances
     */
    Map<Customer, Double> getCustomerOutstandingBalances();

    /**
     * Get aging analysis (30, 60, 90+ days)
     */
    Map<String, List<SalesInvoice>> getAgingAnalysis();

    /**
     * Get total accounts receivable amount
     */
    Double getTotalAccountsReceivable();

    /**
     * Get overdue invoices
     */
    List<SalesInvoice> getOverdueInvoices();

    /**
     * Get invoices by aging period
     */
    List<SalesInvoice> getInvoicesByAgingPeriod(int days);

    /**
     * Get customer payment history
     */
    List<SalesInvoice> getCustomerPaymentHistory(String customerId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Get top customers by outstanding amount
     */
    List<Map<String, Object>> getTopCustomersByOutstanding(int limit);
}
