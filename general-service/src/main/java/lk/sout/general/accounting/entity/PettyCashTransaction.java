package lk.sout.general.accounting.entity;

import lk.sout.core.entity.MetaData;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.mapping.DBRef;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Document
@Component
public class PettyCashTransaction {

    @Id
    private String id;

    @DBRef
    private PettyCash pettyCash;

    @DBRef
    private MetaData transactionType; // "Replenishment", "Expense", "Refund"

    @DBRef
    private MetaData expenseCategory; // Links to existing expense categories

    private Double amount;

    private String description;

    private String voucherNo;

    private String receivedBy; // Person who received the money

    private String approvedBy; // Person who approved the transaction

    private Double balanceAfter; // Balance after this transaction

    private String operator; // "+" for replenishment/refund, "-" for expense

    private String remark;

    @CreatedDate
    private LocalDateTime createdDate;

    @CreatedBy
    private String createdBy;

    @LastModifiedDate
    private LocalDateTime lastModifiedDate;

    @LastModifiedBy
    private String lastModifiedBy;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public PettyCash getPettyCash() {
        return pettyCash;
    }

    public void setPettyCash(PettyCash pettyCash) {
        this.pettyCash = pettyCash;
    }

    public MetaData getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(MetaData transactionType) {
        this.transactionType = transactionType;
    }

    public MetaData getExpenseCategory() {
        return expenseCategory;
    }

    public void setExpenseCategory(MetaData expenseCategory) {
        this.expenseCategory = expenseCategory;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getReceivedBy() {
        return receivedBy;
    }

    public void setReceivedBy(String receivedBy) {
        this.receivedBy = receivedBy;
    }

    public String getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(String approvedBy) {
        this.approvedBy = approvedBy;
    }

    public Double getBalanceAfter() {
        return balanceAfter;
    }

    public void setBalanceAfter(Double balanceAfter) {
        this.balanceAfter = balanceAfter;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public LocalDateTime getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(LocalDateTime createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(LocalDateTime lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    // Helper methods
    public String getTransactionTypeName() {
        return (transactionType != null) ? transactionType.getValue() : "N/A";
    }

    public String getExpenseCategoryName() {
        return (expenseCategory != null) ? expenseCategory.getValue() : "N/A";
    }
}
