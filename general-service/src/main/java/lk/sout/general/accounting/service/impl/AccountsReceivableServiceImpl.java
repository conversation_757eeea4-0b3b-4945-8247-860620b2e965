package lk.sout.general.accounting.service.impl;

import lk.sout.business.accounting.service.AccountsReceivableService;
import lk.sout.business.trade.entity.Customer;
import lk.sout.business.trade.entity.SalesInvoice;
import lk.sout.business.trade.service.CustomerService;
import lk.sout.business.trade.service.SalesInvoiceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccountsReceivableServiceImpl implements AccountsReceivableService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountsReceivableServiceImpl.class);

    @Autowired
    private SalesInvoiceService salesInvoiceService;

    @Autowired
    private CustomerService customerService;

    @Override
    public List<SalesInvoice> getOutstandingInvoices() {
        try {
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();
            LOGGER.info("AccountsReceivableService: Found {} total sales invoices", allInvoices.size());

            List<SalesInvoice> outstandingInvoices = allInvoices.stream()
                    .filter(invoice -> invoice.getBalance() > 0)
                    .sorted((a, b) -> b.getDate().compareTo(a.getDate()))
                    .collect(Collectors.toList());

            LOGGER.info("AccountsReceivableService: Found {} outstanding sales invoices", outstandingInvoices.size());
            return outstandingInvoices;
        } catch (Exception e) {
            LOGGER.error("Error getting outstanding invoices: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SalesInvoice> getOutstandingInvoicesByCustomer(String customerId) {
        try {
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();
            return allInvoices.stream()
                    .filter(invoice -> customerId.equals(invoice.getCustomerNo()) &&
                                     invoice.getBalance() > 0)
                    .sorted((a, b) -> b.getDate().compareTo(a.getDate()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting outstanding invoices by customer: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<Customer, Double> getCustomerOutstandingBalances() {
        try {
            List<SalesInvoice> outstandingInvoices = getOutstandingInvoices();
            Map<String, Double> customerBalances = new HashMap<>();

            // Group by customer number and sum balances
            for (SalesInvoice invoice : outstandingInvoices) {
                if (invoice.getCustomerNo() != null && !invoice.getCustomerNo().isEmpty()) {
                    customerBalances.merge(invoice.getCustomerNo(), invoice.getBalance(), Double::sum);
                }
            }

            // Convert to Customer objects (simplified approach)
            Map<Customer, Double> result = new LinkedHashMap<>();
            for (Map.Entry<String, Double> entry : customerBalances.entrySet()) {
                Customer customer = customerService.findByCustomerNo(entry.getKey());
                if (customer != null) {
                    result.put(customer, entry.getValue());
                }
            }

            return result;
        } catch (Exception e) {
            LOGGER.error("Error getting customer outstanding balances: " + e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, List<SalesInvoice>> getAgingAnalysis() {
        try {
            List<SalesInvoice> outstandingInvoices = getOutstandingInvoices();
            Map<String, List<SalesInvoice>> agingBuckets = new HashMap<>();
            
            agingBuckets.put("0-30 days", new ArrayList<>());
            agingBuckets.put("31-60 days", new ArrayList<>());
            agingBuckets.put("61-90 days", new ArrayList<>());
            agingBuckets.put("90+ days", new ArrayList<>());

            LocalDateTime now = LocalDateTime.now();

            for (SalesInvoice invoice : outstandingInvoices) {
                if (invoice.getDate() != null) {
                    long daysDiff = ChronoUnit.DAYS.between(invoice.getDate(), now.toLocalDate());

                    if (daysDiff <= 30) {
                        agingBuckets.get("0-30 days").add(invoice);
                    } else if (daysDiff <= 60) {
                        agingBuckets.get("31-60 days").add(invoice);
                    } else if (daysDiff <= 90) {
                        agingBuckets.get("61-90 days").add(invoice);
                    } else {
                        agingBuckets.get("90+ days").add(invoice);
                    }
                }
            }

            return agingBuckets;
        } catch (Exception e) {
            LOGGER.error("Error getting aging analysis: " + e.getMessage(), e);
            return new HashMap<>();
        }
    }

    @Override
    public Double getTotalAccountsReceivable() {
        try {
            List<SalesInvoice> outstandingInvoices = getOutstandingInvoices();
            return outstandingInvoices.stream()
                    .mapToDouble(invoice -> invoice.getBalance())
                    .sum();
        } catch (Exception e) {
            LOGGER.error("Error getting total accounts receivable: " + e.getMessage(), e);
            return 0.0;
        }
    }

    @Override
    public List<SalesInvoice> getOverdueInvoices() {
        try {
            List<SalesInvoice> outstandingInvoices = getOutstandingInvoices();
            LocalDateTime now = LocalDateTime.now();

            return outstandingInvoices.stream()
                    .filter(invoice -> {
                        if (invoice.getDueDate() != null) {
                            return invoice.getDueDate().isBefore(now.toLocalDate());
                        }
                        // If no due date, consider invoices older than 30 days as overdue
                        if (invoice.getDate() != null) {
                            long daysDiff = ChronoUnit.DAYS.between(invoice.getDate(), now.toLocalDate());
                            return daysDiff > 30;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting overdue invoices: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SalesInvoice> getInvoicesByAgingPeriod(int days) {
        try {
            List<SalesInvoice> outstandingInvoices = getOutstandingInvoices();
            LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);

            return outstandingInvoices.stream()
                    .filter(invoice -> invoice.getDate() != null &&
                                     invoice.getDate().isBefore(cutoffDate))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting invoices by aging period: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SalesInvoice> getCustomerPaymentHistory(String customerId, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<SalesInvoice> allInvoices = salesInvoiceService.findAll();
            return allInvoices.stream()
                    .filter(invoice -> customerId.equals(invoice.getCustomerNo()) &&
                                     invoice.getDate() != null &&
                                     !invoice.getDate().isBefore(startDate) &&
                                     !invoice.getDate().isAfter(endDate))
                    .sorted((a, b) -> b.getDate().compareTo(a.getDate()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            LOGGER.error("Error getting customer payment history: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getTopCustomersByOutstanding(int limit) {
        try {
            Map<Customer, Double> customerBalances = getCustomerOutstandingBalances();

            List<Map<String, Object>> result = new ArrayList<>();
            int count = 0;
            for (Map.Entry<Customer, Double> entry : customerBalances.entrySet()) {
                if (count >= limit) break;

                Map<String, Object> customerData = new HashMap<>();
                Customer customer = entry.getKey();
                customerData.put("customerId", customer.getId());
                customerData.put("customerName", customer.getName());
                customerData.put("outstandingAmount", entry.getValue());
                result.add(customerData);
                count++;
            }
            return result;
        } catch (Exception e) {
            LOGGER.error("Error getting top customers by outstanding: " + e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
