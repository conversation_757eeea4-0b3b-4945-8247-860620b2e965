package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.entity.Equity;
import lk.sout.business.accounting.service.EquityService;
import lk.sout.core.entity.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/equity")
public class EquityController {

    @Autowired
    private EquityService equityService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Equity equity) {
        try {
            boolean result = equityService.save(equity);
            if (result) {
                Response response = new Response(200, "Equity transaction saved successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to save equity transaction", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error saving equity transaction: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAll(@RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize);
            return ResponseEntity.ok(equityService.findAll(pageable));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(equityService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByType", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByType(@RequestParam("equityType") String equityType) {
        try {
            return ResponseEntity.ok(equityService.findByType(equityType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByFiscalYear", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByFiscalYear(@RequestParam("fiscalYear") String fiscalYear) {
        try {
            return ResponseEntity.ok(equityService.findByFiscalYear(fiscalYear));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByOwner", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByOwner(@RequestParam("ownerName") String ownerName) {
        try {
            return ResponseEntity.ok(equityService.findByOwner(ownerName));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getTotalValue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getTotalValue() {
        try {
            return ResponseEntity.ok(equityService.getTotalEquityValue());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getByTypeValue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getByTypeValue() {
        try {
            return ResponseEntity.ok(equityService.getEquityByTypeValue());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getRetainedEarnings", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getRetainedEarnings() {
        try {
            return ResponseEntity.ok(equityService.getRetainedEarnings());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getTotalCapital", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getTotalCapital() {
        try {
            return ResponseEntity.ok(equityService.getTotalCapital());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/recordProfitRetention", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> recordProfitRetention(@RequestBody Map<String, Object> request) {
        try {
            Double amount = Double.valueOf(request.get("amount").toString());
            String fiscalYear = (String) request.get("fiscalYear");
            String notes = (String) request.get("notes");
            
            boolean result = equityService.recordProfitRetention(amount, fiscalYear, notes);
            if (result) {
                Response response = new Response(200, "Profit retention recorded successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to record profit retention", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error recording profit retention: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/recordOwnerDrawing", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> recordOwnerDrawing(@RequestBody Map<String, Object> request) {
        try {
            Double amount = Double.valueOf(request.get("amount").toString());
            String ownerName = (String) request.get("ownerName");
            String notes = (String) request.get("notes");
            
            boolean result = equityService.recordOwnerDrawing(amount, ownerName, notes);
            if (result) {
                Response response = new Response(200, "Owner drawing recorded successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to record owner drawing", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error recording owner drawing: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/capital", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getCapitalTransactions() {
        try {
            return ResponseEntity.ok(equityService.findCapitalTransactions());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/retainedEarnings", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getRetainedEarningsTransactions() {
        try {
            return ResponseEntity.ok(equityService.findRetainedEarningsTransactions());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/drawings", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getDrawingsTransactions() {
        try {
            return ResponseEntity.ok(equityService.findDrawingsTransactions());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
