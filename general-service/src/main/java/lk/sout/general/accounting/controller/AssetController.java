package lk.sout.general.accounting.controller;

import lk.sout.business.accounting.entity.Asset;
import lk.sout.business.accounting.service.AssetService;
import lk.sout.core.entity.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Map;

@RestController
@RequestMapping("/asset")
public class AssetController {

    @Autowired
    private AssetService assetService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody Asset asset) {
        try {
            boolean result = assetService.save(asset);
            if (result) {
                Response response = new Response(200, "Asset saved successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to save asset", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error saving asset: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAll(@RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize);
            return ResponseEntity.ok(assetService.findAll(pageable));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(assetService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByType", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByType(@RequestParam("assetType") String assetType) {
        try {
            return ResponseEntity.ok(assetService.findByType(assetType));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByCategory", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByCategory(@RequestParam("category") String category) {
        try {
            return ResponseEntity.ok(assetService.findByCategory(category));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByStatus(@RequestParam("status") String status) {
        try {
            return ResponseEntity.ok(assetService.findByStatus(status));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/calculateDepreciation", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> calculateDepreciation(@RequestBody Map<String, String> request) {
        try {
            String id = request.get("id");
            boolean result = assetService.calculateDepreciation(id);
            if (result) {
                Response response = new Response(200, "Depreciation calculated successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to calculate depreciation", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error calculating depreciation: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/getTotalValue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getTotalValue() {
        try {
            return ResponseEntity.ok(assetService.getTotalAssetValue());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getByTypeValue", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getByTypeValue() {
        try {
            return ResponseEntity.ok(assetService.getAssetsByTypeValue());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/dispose", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> dispose(@RequestBody Map<String, Object> request) {
        try {
            String id = (String) request.get("id");
            Date disposalDate = new Date((Long) request.get("disposalDate"));
            Double disposalValue = Double.valueOf(request.get("disposalValue").toString());
            String reason = (String) request.get("reason");
            
            boolean result = assetService.disposeAsset(id, disposalDate, disposalValue, reason);
            if (result) {
                Response response = new Response(200, "Asset disposed successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to dispose asset", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error disposing asset: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/current", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getCurrentAssets() {
        try {
            return ResponseEntity.ok(assetService.findCurrentAssets());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/fixed", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getFixedAssets() {
        try {
            return ResponseEntity.ok(assetService.findFixedAssets());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
