package lk.sout.general.accounting.service;

import lk.sout.business.accounting.entity.Asset;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AssetService {
    
    boolean save(Asset asset);
    
    Iterable<Asset> findAll(Pageable pageable);
    
    Asset findById(String id);
    
    List<Asset> findByType(String assetType);
    
    List<Asset> findByCategory(String category);
    
    List<Asset> findByStatus(String status);
    
    List<Asset> findCurrentAssets();
    
    List<Asset> findFixedAssets();
    
    List<Asset> findActiveAssets();
    
    List<Asset> findDisposedAssets();
    
    List<Asset> findAssetsWithExpiredWarranty();
    
    boolean calculateDepreciation(String id);
    
    Double getTotalAssetValue();
    
    Map<String, Double> getAssetsByTypeValue();
    
    boolean disposeAsset(String id, Date disposalDate, Double disposalValue, String reason);
    
    Double calculateCurrentValue(Asset asset);
    
    Double getTotalCurrentAssetsValue();
    
    Double getTotalFixedAssetsValue();
}
