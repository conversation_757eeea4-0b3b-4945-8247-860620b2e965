package lk.sout.general.hr.service.impl;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.repository.EmployeeRepository;
import lk.sout.business.hr.service.EmployeeService;
import lk.sout.core.repository.MetaDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON> Weerasinghe on 6/18/2018
 */
@Service
public class EmployeeServiceImpl implements EmployeeService {

    final static Logger LOGGER = LoggerFactory.getLogger(EmployeeServiceImpl.class);

    @Autowired
    EmployeeRepository employeeRepository;

    @Autowired
    MetaDataRepository metaDataRepository;

    @Override
    public boolean save(Employee employee) {
        try {
            employeeRepository.save(employee);
            return true;
        } catch (Exception ex) {
            LOGGER.error("employee saving failed" + ex.getMessage());
            return false;
        }
    }

    @Override
    public String remove(Employee employee) {
        try {
            employeeRepository.delete(employee);
            return "success";
        } catch (Exception ex) {
            LOGGER.error("employee deleting failed" + ex.getMessage());
            return ex.getMessage();
        }
    }

    @Override
    public List<Employee> findEmployeeByNameLike(String key) {
        try {
            return employeeRepository.findByNameLikeIgnoreCase(key, true);
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public Employee findByUsername(String username) {
        try {
            return employeeRepository.findByUserName(username);
        } catch (Exception ex) {
            LOGGER.error("Employee search  failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public Optional<Employee> findById(String id) {
        try {
            return employeeRepository.findById(id);
        } catch (Exception e) {
            LOGGER.error("designation  failed" + e.getMessage());
            return null;
        }

    }

    @Override
    public Iterable<Employee> findAll(Pageable pageable) {
        try {
            return employeeRepository.findAll(pageable);
        } catch (Exception ex) {
            LOGGER.error("designation  failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean checkEpf(String epf) {
        try {
            Employee employee = employeeRepository.findByEpfNo(epf);
            if (employee != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Person epf Failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean checkNic(String nic) {
        try {
            Employee employee = employeeRepository.findByNic(nic);
            if (employee != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check Person by NIC Failed: " + ex.getMessage());
            return false;
        }
    }
}
