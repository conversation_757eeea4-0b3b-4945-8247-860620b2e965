package lk.sout.general.hr.service.impl;

import lk.sout.business.hr.entity.SalaryScale;
import lk.sout.business.hr.repository.SalaryScaleRepository;
import lk.sout.business.hr.service.SalaryScaleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SalaryScaleServiceImpl implements SalaryScaleService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalaryScaleServiceImpl.class);

    @Autowired
    SalaryScaleRepository salaryScaleRepository;


    @Override
    public  boolean save(SalaryScale salaryScale){
        try{
            salaryScaleRepository.save(salaryScale);
            return true;
        }catch (Exception ex){
            LOGGER.error("salaryScale save failed" + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<SalaryScale> findAll(Pageable pageable) {
        try {
            return salaryScaleRepository.findAll(pageable);
//            return null;
        }catch (Exception e){
            LOGGER.error("salaryScale searching failed" + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryScale> findByName(String any) {
        try {
            return salaryScaleRepository.findAllByNameLikeIgnoreCase(any);
        }catch (Exception e){
            LOGGER.error("salaryScale searching failed" + e.getMessage());
            return null;
        }
    }

    @Override
    public SalaryScale findById(String id) {
        try {
            return salaryScaleRepository.findSalaryScaleById(id);
        }catch (Exception e){
            LOGGER.error("salaryScale searching failed" + e.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryScale> findAllSalaryScale() {
        try {
            return salaryScaleRepository.findAll();
        }catch (Exception e){
            LOGGER.error("salaryScale searching failed" + e.getMessage());
            return null;
        }
    }

    @Override
    public boolean findBySalaryScaleName(String name) {
        try {
            SalaryScale salaryScale = salaryScaleRepository.findByName(name);
            if (salaryScale != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("check addLeave Failed: " + ex.getMessage());
            return false;
        }
    }    }








