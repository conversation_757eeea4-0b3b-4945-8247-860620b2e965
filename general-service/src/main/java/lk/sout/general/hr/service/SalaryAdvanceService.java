package lk.sout.general.hr.service;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.entity.SalaryAdvance;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SalaryAdvanceService {
    
    boolean save(SalaryAdvance salaryAdvance);
    
    Iterable<SalaryAdvance> findAll(Pageable pageable);
    
    SalaryAdvance findById(String id);
    
    List<SalaryAdvance> findByEmployee(Employee employee);
    
    List<SalaryAdvance> findByEmployeeId(String employeeId);
    
    List<SalaryAdvance> findByStatus(String status);
    
    boolean approve(String id, String approvedBy);
    
    boolean reject(String id, String remarks);
    
    boolean markAsPaid(String id);
    
    List<SalaryAdvance> getPendingAdvances();
    
    List<SalaryAdvance> getApprovedAdvances();
    
    List<SalaryAdvance> getPaidAdvances();
    
    List<SalaryAdvance> getActiveAdvancesByEmployee(String employeeId);
    
    List<SalaryAdvance> getOutstandingAdvancesByEmployee(String employeeId);
    
    Double getTotalOutstandingByEmployee(String employeeId);
}
