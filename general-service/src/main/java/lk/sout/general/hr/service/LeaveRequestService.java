package lk.sout.general.hr.service;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.entity.LeaveRequest;
import org.springframework.data.domain.Pageable;

import java.util.Date;
import java.util.List;

public interface LeaveRequestService {

    boolean save(LeaveRequest leaveRequest);

    Iterable<LeaveRequest> findAll(Pageable pageable);

    List<LeaveRequest> findByEpf(String any);

    boolean findByDatesAndEmployee(Date from, Date to, String employee);

    // New methods for status-based filtering
    Iterable<LeaveRequest> findPendingLeaves(Pageable pageable);

    List<LeaveRequest> findByLeaveStatus(String status);

    List<LeaveRequest> findByEmployeeAndDateRange(Employee employee, Date from, Date to);

    List<LeaveRequest> findByLeaveTypeAndDateRange(String leaveType, Date from, Date to);

    List<LeaveRequest> findAllLeaves();
}

