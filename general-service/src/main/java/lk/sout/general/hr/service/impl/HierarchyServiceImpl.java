package lk.sout.general.hr.service.impl;

import lk.sout.business.hr.entity.Hierarchy;
import lk.sout.business.hr.repository.EmployeeRepository;
import lk.sout.business.hr.repository.HierarchyRepository;
import lk.sout.business.hr.service.HierarchyService;
import lk.sout.core.repository.MetaDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class HierarchyServiceImpl implements HierarchyService {

    final static Logger LOGGER = LoggerFactory.getLogger(HierarchyServiceImpl.class);

    @Autowired
    HierarchyRepository hierarchyRepository;

    @Autowired
    MetaDataRepository metaDataRepository;

    @Autowired
    EmployeeRepository employeeRepository;

    @Override
    public boolean save(Hierarchy hierarchy) {
        try {
            hierarchyRepository.save(hierarchy);
            return true;
        } catch (Exception ex) {
            LOGGER.error("hierarchy saving failed" + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<Hierarchy> findAll() {
        try {
            return hierarchyRepository.findAll();

        } catch (Exception ex) {
            LOGGER.error("hierarchy failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<Hierarchy> findByReportingManager(String key) {
        try {
            return hierarchyRepository.findAllByReportingManager(employeeRepository.findByUserName(key));
        } catch (Exception ex) {
            LOGGER.error("Retrieving a manager failed");
            return null;
        }
    }

    @Override
    public Iterable<Hierarchy> findAll(Pageable pageable) {
        try {
            Iterable<Hierarchy> hierarchies = hierarchyRepository.findAll(pageable);
            return hierarchies;
        } catch (Exception ex) {
            LOGGER.error("add leave   failed" + ex.getMessage());
            return null;
        }

    }
}
