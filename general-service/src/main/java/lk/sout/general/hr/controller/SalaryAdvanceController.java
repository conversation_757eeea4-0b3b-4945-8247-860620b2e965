package lk.sout.general.hr.controller;

import lk.sout.business.hr.entity.SalaryAdvance;
import lk.sout.business.hr.service.SalaryAdvanceService;
import lk.sout.core.entity.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/salaryAdvance")
public class SalaryAdvanceController {

    @Autowired
    private SalaryAdvanceService salaryAdvanceService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> save(@RequestBody SalaryAdvance salaryAdvance) {
        try {
            boolean result = salaryAdvanceService.save(salaryAdvance);
            if (result) {
                Response response = new Response(200, "Salary advance saved successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to save salary advance", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error saving salary advance: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/findAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findAll(@RequestParam("page") int page, @RequestParam("pageSize") int pageSize) {
        try {
            Pageable pageable = PageRequest.of(page, pageSize);
            return ResponseEntity.ok(salaryAdvanceService.findAll(pageable));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findById(@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(salaryAdvanceService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByEmployee", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByEmployee(@RequestParam("employeeId") String employeeId) {
        try {
            return ResponseEntity.ok(salaryAdvanceService.findByEmployeeId(employeeId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByStatus", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> findByStatus(@RequestParam("status") String status) {
        try {
            return ResponseEntity.ok(salaryAdvanceService.findByStatus(status));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/approve", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> approve(@RequestBody Map<String, String> request) {
        try {
            String id = request.get("id");
            String approvedBy = request.get("approvedBy");
            boolean result = salaryAdvanceService.approve(id, approvedBy);
            if (result) {
                Response response = new Response(200, "Salary advance approved successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to approve salary advance", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error approving salary advance: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/reject", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> reject(@RequestBody Map<String, String> request) {
        try {
            String id = request.get("id");
            String remarks = request.get("remarks");
            boolean result = salaryAdvanceService.reject(id, remarks);
            if (result) {
                Response response = new Response(200, "Salary advance rejected successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to reject salary advance", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error rejecting salary advance: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/markAsPaid", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    private ResponseEntity<?> markAsPaid(@RequestBody Map<String, String> request) {
        try {
            String id = request.get("id");
            boolean result = salaryAdvanceService.markAsPaid(id);
            if (result) {
                Response response = new Response(200, "Salary advance marked as paid successfully", true);
                return ResponseEntity.ok(response);
            } else {
                Response response = new Response(500, "Failed to mark salary advance as paid", false);
                return ResponseEntity.ok(response);
            }
        } catch (Exception ex) {
            Response response = new Response(500, "Error marking salary advance as paid: " + ex.getMessage(), false);
            return ResponseEntity.ok(response);
        }
    }

    @RequestMapping(value = "/pending", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getPendingAdvances() {
        try {
            return ResponseEntity.ok(salaryAdvanceService.getPendingAdvances());
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/byEmployee", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getAdvancesByEmployee(@RequestParam("employeeId") String employeeId) {
        try {
            return ResponseEntity.ok(salaryAdvanceService.getActiveAdvancesByEmployee(employeeId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/outstanding", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    private ResponseEntity<?> getOutstandingByEmployee(@RequestParam("employeeId") String employeeId) {
        try {
            return ResponseEntity.ok(salaryAdvanceService.getTotalOutstandingByEmployee(employeeId));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
