package lk.sout.general.hr.service.impl;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.entity.LeaveRequest;
import lk.sout.business.hr.repository.EmployeeRepository;
import lk.sout.business.hr.repository.LeaveRequestRepository;
import lk.sout.business.hr.service.LeaveRequestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;


@Service
public class LeaveRequestServiceImpl implements LeaveRequestService {

    final static Logger LOGGER = LoggerFactory.getLogger(LeaveRequestServiceImpl.class);

    @Autowired
    LeaveRequestRepository leaveRequestRepository;

    @Autowired
    EmployeeRepository employeeRepository;


    @Override
    public boolean save(LeaveRequest leaveRequest) {
        try {
            leaveRequestRepository.save(leaveRequest);
            return true;
        } catch (Exception ex) {
            LOGGER.error(" LeaveRequest saving failed" + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<LeaveRequest> findAll(Pageable pageable) {
        try {
            return leaveRequestRepository.findAll(pageable);

        } catch (Exception ex) {
            LOGGER.error("LeaveRequest failed" + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LeaveRequest> findByEpf(String any) {
        try {
            List<LeaveRequest> leaveRequests = leaveRequestRepository.findByEpfLikeIgnoreCase(any);
            return leaveRequests;
        } catch (Exception ex) {
            LOGGER.error("Retrieving a brand failed");
            return null;
        }
    }

    @Override
    public boolean findByDatesAndEmployee(Date from, Date to, String employee) {
        try {
            //query not working correctly
            List<LeaveRequest> leaveRequests = leaveRequestRepository.findByFromAfterAndToBeforeAndEmployee(from, to, employeeRepository.findById(employee).get());
            if (leaveRequests.size() > 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            LOGGER.error("Retrieving a leaves failed");
            return false;
        }
    }

    @Override
    public Iterable<LeaveRequest> findPendingLeaves(Pageable pageable) {
        try {
            return leaveRequestRepository.findPendingLeaves(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving pending leaves failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LeaveRequest> findByLeaveStatus(String status) {
        try {
            return leaveRequestRepository.findByLeaveStatusValue(status);
        } catch (Exception ex) {
            LOGGER.error("Retrieving leaves by status failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LeaveRequest> findByEmployeeAndDateRange(Employee employee, Date from, Date to) {
        try {
            return leaveRequestRepository.findByEmployeeAndFromGreaterThanEqualAndToLessThanEqual(employee, from, to);
        } catch (Exception ex) {
            LOGGER.error("Retrieving leaves by employee and date range failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LeaveRequest> findByLeaveTypeAndDateRange(String leaveType, Date from, Date to) {
        try {
            return leaveRequestRepository.findByLeaveTypeAndDateRange(leaveType, from, to);
        } catch (Exception ex) {
            LOGGER.error("Retrieving leaves by type and date range failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<LeaveRequest> findAllLeaves() {
        try {
            return leaveRequestRepository.findAll();
        } catch (Exception ex) {
            LOGGER.error("Retrieving all leaves failed: " + ex.getMessage());
            return null;
        }
    }
}
