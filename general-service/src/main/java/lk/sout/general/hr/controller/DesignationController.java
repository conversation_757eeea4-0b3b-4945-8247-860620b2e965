package lk.sout.general.hr.controller;

import lk.sout.business.hr.entity.Designation;
import lk.sout.business.hr.service.DesignationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/designation")
public class DesignationController {
    @Autowired
    DesignationService designationService;

    @RequestMapping(value = "/save", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody

    private ResponseEntity<?> save(@RequestBody Designation designation){
        try{
            return  ResponseEntity.ok(designationService.save(designation));
        }catch (Exception e){
            return  ResponseEntity.status(HttpStatus.CONFLICT).build();
        }

    }

    @RequestMapping(value = "/findAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>findAll(@RequestParam("page") String page, @RequestParam("pageSize") String pageSize) {
        try {
            return ResponseEntity.ok(designationService.findAll(PageRequest.of(Integer.parseInt(page), Integer.parseInt(pageSize))));
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/getAll" ,method = RequestMethod.GET)
    private ResponseEntity<?>getAll() {
        try {
            return ResponseEntity.ok(designationService.findAll());
        }catch (Exception e){
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findAllByDesignationLike", method = RequestMethod.GET)
    private ResponseEntity<?> findAllByDesignationLike (@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(designationService.searchDesignationNameLike(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findById", method = RequestMethod.GET)
    private ResponseEntity<?> findById (@RequestParam("id") String id) {
        try {
            return ResponseEntity.ok(designationService.findById(id));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }

    @RequestMapping(value = "/findByDesignationName", method = RequestMethod.GET)
    private ResponseEntity<?> findByDesignationName (@RequestParam("name") String name) {
        try {
            return ResponseEntity.ok(designationService.findByDesignationName(name));
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.CONFLICT).build();
        }
    }
}
