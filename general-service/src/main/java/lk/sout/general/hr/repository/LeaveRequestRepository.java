package lk.sout.general.hr.repository;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.entity.LeaveRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Date;
import java.util.List;

public interface LeaveRequestRepository extends MongoRepository<LeaveRequest, String> {
    Page<LeaveRequest> findAll(Pageable pageable);

    List<LeaveRequest> findByEpfLikeIgnoreCase(String any);

    List<LeaveRequest> findByFromAfterAndToBeforeAndEmployee(Date from, Date to, Employee employee);

    // New methods for status-based filtering
    @Query("{'leaveStatus': null}")
    Page<LeaveRequest> findPendingLeaves(Pageable pageable);

    @Query("{'leaveStatus.value': ?0}")
    List<LeaveRequest> findByLeaveStatusValue(String status);

    List<LeaveRequest> findByEmployeeAndFromGreaterThanEqualAndToLessThanEqual(Employee employee, Date from, Date to);

    @Query("{'type.leaveType': ?0, 'from': {$gte: ?1}, 'to': {$lte: ?2}}")
    List<LeaveRequest> findByLeaveTypeAndDateRange(String leaveType, Date from, Date to);
}
