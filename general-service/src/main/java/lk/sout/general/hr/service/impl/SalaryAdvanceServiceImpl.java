package lk.sout.general.hr.service.impl;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.entity.SalaryAdvance;
import lk.sout.business.hr.repository.SalaryAdvanceRepository;
import lk.sout.business.hr.service.SalaryAdvanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
public class SalaryAdvanceServiceImpl implements SalaryAdvanceService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SalaryAdvanceServiceImpl.class);

    @Autowired
    private SalaryAdvanceRepository salaryAdvanceRepository;

    @Override
    public boolean save(SalaryAdvance salaryAdvance) {
        try {
            if (salaryAdvance.getId() == null) {
                // New salary advance - validate business rules

                // Check if employee has salary scale
                if (salaryAdvance.getEmployee() == null ||
                    salaryAdvance.getEmployee().getSalaryScale() == null) {
                    LOGGER.error("Employee does not have a salary scale assigned");
                    return false;
                }

                double employeeSalary = salaryAdvance.getEmployee().getSalaryScale().getBasicSalary();

                // Check if salary is greater than 0
                if (employeeSalary <= 0) {
                    LOGGER.error("Employee's basic salary is not set or is zero");
                    return false;
                }

                // Check if advance amount exceeds salary
                if (salaryAdvance.getAdvanceAmount() > employeeSalary) {
                    LOGGER.error("Advance amount exceeds employee's basic salary");
                    return false;
                }

                // Check total outstanding advances for this employee
                List<SalaryAdvance> outstandingAdvances = getOutstandingAdvancesByEmployee(salaryAdvance.getEmployee().getId());
                double totalOutstanding = 0.0;
                if (outstandingAdvances != null) {
                    totalOutstanding = outstandingAdvances.stream()
                            .mapToDouble(advance -> advance.getRemainingAmount() != null ? advance.getRemainingAmount() : 0.0)
                            .sum();
                }

                // Check if total advances (existing + new) exceed salary
                if ((totalOutstanding + salaryAdvance.getAdvanceAmount()) > employeeSalary) {
                    LOGGER.error("Total advance amount (including existing advances) exceeds employee's basic salary");
                    return false;
                }

                // Set default values for new advance
                salaryAdvance.setRequestDate(new Date());
                salaryAdvance.setStatus("PENDING");
                salaryAdvance.setActive(true);
                salaryAdvance.setRemainingAmount(salaryAdvance.getAdvanceAmount());

                // Set as single payment (no installments as per requirement)
                salaryAdvance.setNumberOfInstallments(1);
                salaryAdvance.setInstallmentAmount(salaryAdvance.getAdvanceAmount());
            }

            salaryAdvanceRepository.save(salaryAdvance);
            return true;
        } catch (Exception ex) {
            LOGGER.error("Saving salary advance failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public Iterable<SalaryAdvance> findAll(Pageable pageable) {
        try {
            return salaryAdvanceRepository.findByActiveTrue(pageable);
        } catch (Exception ex) {
            LOGGER.error("Retrieving salary advances failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public SalaryAdvance findById(String id) {
        try {
            Optional<SalaryAdvance> salaryAdvance = salaryAdvanceRepository.findById(id);
            return salaryAdvance.orElse(null);
        } catch (Exception ex) {
            LOGGER.error("Retrieving salary advance by id failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> findByEmployee(Employee employee) {
        try {
            return salaryAdvanceRepository.findByEmployeeAndActiveTrue(employee);
        } catch (Exception ex) {
            LOGGER.error("Retrieving salary advances by employee failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> findByEmployeeId(String employeeId) {
        try {
            return salaryAdvanceRepository.findByEmployeeIdAndActiveTrue(employeeId);
        } catch (Exception ex) {
            LOGGER.error("Retrieving salary advances by employee id failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> findByStatus(String status) {
        try {
            return salaryAdvanceRepository.findByStatusAndActiveTrue(status);
        } catch (Exception ex) {
            LOGGER.error("Retrieving salary advances by status failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public boolean approve(String id, String approvedBy) {
        try {
            Optional<SalaryAdvance> optionalAdvance = salaryAdvanceRepository.findById(id);
            if (optionalAdvance.isPresent()) {
                SalaryAdvance advance = optionalAdvance.get();
                advance.setStatus("APPROVED");
                advance.setApprovedBy(approvedBy);
                advance.setApprovedDate(new Date());
                salaryAdvanceRepository.save(advance);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Approving salary advance failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean reject(String id, String remarks) {
        try {
            Optional<SalaryAdvance> optionalAdvance = salaryAdvanceRepository.findById(id);
            if (optionalAdvance.isPresent()) {
                SalaryAdvance advance = optionalAdvance.get();
                advance.setStatus("REJECTED");
                advance.setRemarks(remarks);
                salaryAdvanceRepository.save(advance);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Rejecting salary advance failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public boolean markAsPaid(String id) {
        try {
            Optional<SalaryAdvance> optionalAdvance = salaryAdvanceRepository.findById(id);
            if (optionalAdvance.isPresent()) {
                SalaryAdvance advance = optionalAdvance.get();
                advance.setStatus("PAID");
                advance.setPaidDate(new Date());
                salaryAdvanceRepository.save(advance);
                return true;
            }
            return false;
        } catch (Exception ex) {
            LOGGER.error("Marking salary advance as paid failed: " + ex.getMessage());
            return false;
        }
    }

    @Override
    public List<SalaryAdvance> getPendingAdvances() {
        try {
            return salaryAdvanceRepository.findPendingAdvances();
        } catch (Exception ex) {
            LOGGER.error("Retrieving pending salary advances failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> getApprovedAdvances() {
        try {
            return salaryAdvanceRepository.findApprovedAdvances();
        } catch (Exception ex) {
            LOGGER.error("Retrieving approved salary advances failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> getPaidAdvances() {
        try {
            return salaryAdvanceRepository.findPaidAdvances();
        } catch (Exception ex) {
            LOGGER.error("Retrieving paid salary advances failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> getActiveAdvancesByEmployee(String employeeId) {
        try {
            return salaryAdvanceRepository.findActiveAdvancesByEmployee(employeeId);
        } catch (Exception ex) {
            LOGGER.error("Retrieving active advances by employee failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public List<SalaryAdvance> getOutstandingAdvancesByEmployee(String employeeId) {
        try {
            return salaryAdvanceRepository.findOutstandingAdvancesByEmployee(employeeId);
        } catch (Exception ex) {
            LOGGER.error("Retrieving outstanding advances by employee failed: " + ex.getMessage());
            return null;
        }
    }

    @Override
    public Double getTotalOutstandingByEmployee(String employeeId) {
        try {
            List<SalaryAdvance> outstandingAdvances = getOutstandingAdvancesByEmployee(employeeId);
            if (outstandingAdvances != null) {
                return outstandingAdvances.stream()
                        .mapToDouble(advance -> advance.getRemainingAmount() != null ? advance.getRemainingAmount() : 0.0)
                        .sum();
            }
            return 0.0;
        } catch (Exception ex) {
            LOGGER.error("Calculating total outstanding by employee failed: " + ex.getMessage());
            return 0.0;
        }
    }
}
