package lk.sout.general.hr.repository;

import lk.sout.business.hr.entity.Employee;
import lk.sout.business.hr.entity.SalaryAdvance;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SalaryAdvanceRepository extends MongoRepository<SalaryAdvance, String> {
    
    Page<SalaryAdvance> findByActiveTrue(Pageable pageable);
    
    List<SalaryAdvance> findByEmployeeAndActiveTrue(Employee employee);
    
    List<SalaryAdvance> findByStatusAndActiveTrue(String status);
    
    List<SalaryAdvance> findByEmployeeIdAndActiveTrue(String employeeId);
    
    @Query("{'status': 'PENDING', 'active': true}")
    List<SalaryAdvance> findPendingAdvances();
    
    @Query("{'status': 'APPROVED', 'active': true}")
    List<SalaryAdvance> findApprovedAdvances();
    
    @Query("{'status': 'PAID', 'active': true}")
    List<SalaryAdvance> findPaidAdvances();
    
    @Query("{'employee.id': ?0, 'status': {$in: ['APPROVED', 'PAID']}, 'active': true}")
    List<SalaryAdvance> findActiveAdvancesByEmployee(String employeeId);
    
    @Query("{'employee.id': ?0, 'remainingAmount': {$gt: 0}, 'active': true}")
    List<SalaryAdvance> findOutstandingAdvancesByEmployee(String employeeId);
}
