import {NgModule} from '@angular/core';
import {AppRoutingModule, routeParams} from './app-routing.module';
import {AppComponent} from './app.component';
import {CommonModule} from '@angular/common';
import {BrowserModule} from '@angular/platform-browser';
import {FormsModule} from '@angular/forms';

import {CoreModule} from './home/<USER>/core.module';
import {InventoryModule} from './home/<USER>/inventory/inventory.module';
import {AdminModule} from './home/<USER>/admin.module';
import {TradeModule} from './home/<USER>/trade/trade.module';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {ReportModule} from "./home/<USER>/report/report.module";

import {SettingsAccessService} from "./home/<USER>/service/settings-access.service";
import {UserSettingsService} from "./home/<USER>/service/user-settings.service";
import {TranslatePipe} from './translate.pipe';
import {TranslateService} from './translate.service';

@NgModule({
  declarations: [
    AppComponent,
    routeParams
  ],
  imports: [
    CommonModule,
    BrowserAnimationsModule,
    BrowserModule,
    FormsModule,
    AppRoutingModule,
    CoreModule,
    AdminModule,
    InventoryModule,
    TradeModule,
    ReportModule
  ],
  bootstrap: [AppComponent],
  providers: [
    UserSettingsService,
    SettingsAccessService,
    TranslateService
  ]
})

export class AppModule {
}














