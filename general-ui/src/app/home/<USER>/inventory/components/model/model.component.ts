import {Component, OnInit, TemplateRef} from '@angular/core';
import {Brand} from "../../model/brand";
import {ModelService} from "../../service/model.service";
import {NotificationService} from "../../../../core/service/notification.service";
import {BrandService} from "../../service/brand.service";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Model} from "../../model/model";

@Component({
  standalone: false,
  selector: 'app-model',
  templateUrl: './model.component.html',
  styleUrls: ['./model.component.css']
})
export class ModelComponent implements OnInit {

  model: Model;
  models: Array<Model> = [];
  setClickedRow: Function;
  selectedRow: number;
  keyModel: string;
  collectionSize;
  page;
  pageSize;
  keyBrand: string;
  brands: Array<Brand>;
  modalRef: BsModalRef;

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  constructor(private modelService: ModelService,
              private notificationService: NotificationService,
              private brandService: BrandService,
               private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.model = new Model();
    this.page = 1;
    this.pageSize = 8;
    this.model.active = true;
    this.findAll();

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  findAll() {
    this.modelService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.models = data.content;
      this.collectionSize = data.totalPages * 8;
    })
  }

  loadModels() {
    this.modelService.findByName(this.keyModel).subscribe((data: Array<Model>) => {
      this.models = data;
    });
  }

  setSelectedModel($event: any) {
    this.model = $event.item;
  }

  modelDetail(model, index) {
    this.model = model;
    this.selectedRow = index;
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  saveModel() {
    // Validate that a brand is selected
    if (!this.model.brand) {
      this.notificationService.showError("Please select a brand before saving the model");
      return;
    }

    this.modelService.save(this.model).subscribe(result => {
      if (result && result.code === 200) {
        this.notificationService.showSuccess(result.message || "Model Saved Successfully");
        this.clear();
        this.ngOnInit();
      } else {
        this.notificationService.showError(result?.message || "Model Save Failed");
      }
    }, error => {
      this.notificationService.showError("Model Save Failed: " + (error.error?.message || error.message));
      console.log(error);
    });
  }

  updateModel() {
    this.saveModel();
  }

  clear() {
    this.model = new Model();
    this.model.active = true;
    this.keyBrand = '';
    this.brands = [];
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  setSelectedBrand($event: any) {
    this.model.brand = $event.item;
  }

  openModalLarge(template: TemplateRef<any>) {
    this.modalRef = this.modalService.show(template, {class: 'modal-lg'} as ModalOptions);
  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }
}
