<div class="container-fluid" [ngClass]="{'px-0': !isModal, 'p-3': isModal}">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h2 class="component-title mb-0">
      {{ 'VIEW_STOCK.TITLE' | translate }}
      <span *ngIf="itemName" class="text-primary ml-2">
        - {{ itemName }}
      </span>
    </h2>
    <button *ngIf="isModal" type="button" class="close" aria-label="Close" (click)="closeModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="row g-3 mb-4">
      <div class="col-12 col-md-2">
        <div class="form-group">
          <label class="form-label fw-bold d-block d-md-none">Warehouse</label>
          <select class="form-control form-control-lg" required #selectedWh="ngModel"
                  [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
                  [(ngModel)]="selectedWarehouse" (change)="filterByWarehouse()">
            <option [ngValue]="">Select Warehouse</option>
            <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
          </select>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-3">
        <div class="form-group">
          <label class="form-label fw-bold d-block d-md-none">Search By Items</label>
          <div class="input-group">
            <input [(ngModel)]="keyItemSearch"
                   [typeahead]="itemSearched"
                   (typeaheadLoading)="loadItems()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="itemName"
                   placeholder="Search By Items"
                   autocomplete="off"
                   class="form-control form-control-lg" name="searchItem">
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-3">
        <div class="form-group">
          <label class="form-label fw-bold d-block d-md-none">Search By Barcode</label>
          <div class="input-group">
            <input [(ngModel)]="barcode"
                   [typeahead]="itemSearched"
                   (typeaheadLoading)="loadItemByCode()"
                   (typeaheadOnSelect)="setSelectedItem($event)"
                   [typeaheadOptionsLimit]="7"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="barcode"
                   autocomplete="off"
                   placeholder="Search By Barcode"
                   class="form-control form-control-lg" name="category">
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-2">
        <div class="form-group">
          <label class="form-label fw-bold d-block d-md-none">Category</label>
          <div class="input-group">
            <input [(ngModel)]="keyItemCategory"
                   [typeahead]="itemCategories"
                   (typeaheadLoading)="loadItemCategories()"
                   (typeaheadOnSelect)="setSelectedItemCategory($event)"
                   [typeaheadOptionsLimit]="15"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="categoryName"
                   placeholder="Category"
                   autocomplete="off"
                   #category="ngModel"
                   class="form-control form-control-lg" name="category">
          </div>
        </div>
      </div>
      <div class="col-12 col-sm-6 col-md-2">
        <div class="form-group">
          <label class="form-label fw-bold d-block d-md-none">Brand</label>
          <div class="input-group">
            <input [(ngModel)]="keyBrand"
                   [typeahead]="brands"
                   (typeaheadLoading)="loadBrands()"
                   (typeaheadOnSelect)="setSelectedBrand($event)"
                   [typeaheadOptionsLimit]="10"
                   typeaheadWaitMs="1000"
                   typeaheadOptionField="name"
                   placeholder="Brand"
                   autocomplete="off"
                   class="form-control form-control-lg" name="brand">
          </div>
        </div>
      </div>
    </div>
    <div class="table-responsive">
      <table class="table table-striped table-hover mt-2">
        <thead class="table-light">
        <tr class="text-center">
          <th class="d-none d-md-table-cell">Warehouse</th>
          <th>Barcode</th>
          <th>Item Name</th>
          <th class="d-none d-md-table-cell">Selling Price</th>
          <th class="d-none d-md-table-cell">Dead Stock Level</th>
          <th>Quantity</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let stock of subStocks,let i = index"
            (click)="selectStockRecord(stock,i)"
            [class.active]="i === selectedRow" class="text-center">
          <td class="d-none d-md-table-cell">{{stock.warehouseName}}</td>
          <td>{{stock.barcode}}</td>
          <td>{{stock.itemName}}</td>
          <td class="d-none d-md-table-cell">{{stock.sellingPrice}}</td>
          <td class="d-none d-md-table-cell">{{stock.deadStockLevel}}</td>
          <td>{{stock.quantity}}</td>
        </tr>
        <tr *ngIf="!loading && (!subStocks || subStocks.length === 0)">
          <td colspan="6" class="text-center py-4">
            <div class="alert alert-info mb-0">
              <i class="fas fa-info-circle mr-2"></i>
              No stock records found for this item.
            </div>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Mobile view for selected stock details -->
    <div class="d-md-none mt-3 mb-3" *ngIf="selectedStock && selectedStock.id">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">Selected Stock Details</h5>
          <div class="row g-2">
            <div class="col-6">
              <p class="mb-1 fw-bold">Warehouse:</p>
              <p>{{selectedStock.warehouseName}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Selling Price:</p>
              <p>{{selectedStock.sellingPrice}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Dead Stock Level:</p>
              <p>{{selectedStock.deadStockLevel}}</p>
            </div>
            <div class="col-6">
              <p class="mb-1 fw-bold">Quantity:</p>
              <p>{{selectedStock.quantity}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-12">
        <pagination class="pagination justify-content-center"
                    [totalItems]="collectionSize"
                    [(ngModel)]="page"
                    (pageChanged)="pageChanged($event)"
                    [maxSize]="5"
                    [boundaryLinks]="true">
        </pagination>
      </div>
    </div>
    <div class="row mt-3">
      <div class="col-12 text-right">
        <button type="button" class="btn btn-primary ml-2" (click)="clearTable()">Reset Table</button>
        <button type="button" class="btn btn-outline-danger ml-2" (click)="transferStock()">Transfer</button>
        <button type="button" class="btn btn-outline-danger ml-2" (click)="adjustStock()">Adjust Stock</button>
      </div>
    </div>
  <ngx-spinner type="ball-scale-multiple"></ngx-spinner>
</div>

