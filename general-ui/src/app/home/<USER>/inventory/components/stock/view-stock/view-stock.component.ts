import {Component, OnInit} from '@angular/core';
import {Item} from '../../../model/item';
import {ItemService} from '../../../service/item.service';
import {Warehouse} from '../../../model/warehouse';
import {WarehouseService} from '../../../service/warehouse.service';
import {TransferStockComponent} from "../transfer-stock/transfer-stock.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {Stock} from "../../../model/stock";
import {StockService} from "../../../service/stock.service";
import {AdjustStockComponent} from "../adjust-stock/adjust-stock.component";
import {User} from "../../../../../admin/model/user";
import {ItemCategoryService} from "../../../service/item-category.service";
import {BrandService} from "../../../service/brand.service";
import {ItemCategory} from "../../../model/item-category";
import {Brand} from "../../../model/brand";

@Component({
  standalone: false,
  selector: 'app-view-sub-stock',
  templateUrl: './view-stock.component.html',
  styleUrls: ['./view-stock.component.css']
})
export class ViewStockComponent implements OnInit {

  page;
  pageSize;
  collectionSize;

  subStocks: Array<Stock> = [];
  warehouses: Array<Warehouse> = [];
  isModal: boolean = false;

  selectedRow: number;

  keyItemSearch: string;
  itemSearched: Array<Item> = [];

  selectedStock = new Stock();
  barcode: string;
  selectedWarehouse: Warehouse;

  keyItemCategory: string;
  itemCategories: Array<ItemCategory> = [];
  selectedItemCategory = new ItemCategory();
  keyBrand: string;
  brands: Array<Brand> = [];
  selectedBrand = new Brand();

  user: User;

  modalRef: BsModalRef;

  loading: boolean;

  // Parameters passed when opening as a modal to show stock for a specific item
  itemBarcode: string;
  itemName: string;

  constructor(private stockService: StockService, private warehouseService: WarehouseService,
              private itemCategoryService: ItemCategoryService,
              private brandService: BrandService,
              private itemService: ItemService, private modalService: BsModalService) {
  }

  /**
   * Close the modal
   */
  closeModal() {
    // When opened as a modal from another component, this.modalRef might not be set
    // In that case, we need to use the BsModalService to hide the modal
    if (this.modalRef) {
      this.modalRef.hide();
    } else if (this.isModal) {
      // If we're in a modal but don't have a reference, use the modalService
      this.modalService.hide();
    }
  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.loadWarehouses();
    this.selectedWarehouse = new Warehouse();
    this.user = JSON.parse(localStorage.getItem('currentUser')).user;

    console.log('ViewStockComponent initialized with itemBarcode:', this.itemBarcode);

    // If itemBarcode is provided, we'll load stock for that specific item
    if (this.itemBarcode) {
      this.loading = true;
      this.barcode = this.itemBarcode;

      // Set a default warehouse code in case the loadUserWarehouse fails
      this.selectedWarehouse.code = 0;

      // Try to load the user's warehouse first
      this.loadUserWarehouse(() => {
        // After warehouse is loaded, search for the item stock
        this.searchStockByBarcode();
      });

      // Also directly call searchStockByBarcode as a fallback
      // This ensures we at least try to search even if warehouse loading fails
      setTimeout(() => {
        if (this.loading && (!this.subStocks || this.subStocks.length === 0)) {
          console.log('Fallback: Directly searching for stock by barcode');
          this.searchStockByBarcode();
        }
      }, 1000); // Wait 1 second before trying the fallback
    } else {
      // Normal initialization without specific item
      this.loadUserWarehouse();
    }
  }

  /**
   * Load the user's warehouse
   * @param callback Optional callback function to execute after warehouse is loaded
   */
  loadUserWarehouse(callback?: Function) {
    this.warehouseService.findByCode(0).subscribe(
      (result: Warehouse) => {
        console.log('Warehouse loaded:', result);
        this.selectedWarehouse = result;

        if (callback) {
          // If a callback was provided, execute it
          console.log('Executing callback after loading warehouse');
          callback();
        } else {
          // Otherwise, just filter by warehouse as usual
          this.filterByWarehouse();
        }
      },
      (error) => {
        console.error('Error loading warehouse:', error);
        this.loading = false;

        // If we have a barcode, we can still try to search for stock
        if (this.barcode && callback) {
          // Use a default warehouse code of 0 if we couldn't load the warehouse
          this.selectedWarehouse = new Warehouse();
          this.selectedWarehouse.code = 0;
          console.log('Using default warehouse code 0');
          callback();
        }
      }
    )
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.filterByWarehouse();
  }

  loadWarehouses() {
    this.warehouseService.findAllActive().subscribe((result: Array<Warehouse>) => {
      if (result.length === 1) {
        this.selectedWarehouse = result[0];
      }
      return this.warehouses = result;
    })
  }

  loadItemCategories() {
    return this.itemCategoryService.findByName(this.keyItemCategory).subscribe((data: Array<ItemCategory>) => {
      return this.itemCategories = data;
    });
  }

  setSelectedItemCategory(event) {
    this.loading = true;
    this.stockService.findStockByItemCategoryAndWh(event.item.code, this.selectedWarehouse.code).subscribe((data: Array<Stock>) => {
      this.subStocks = data;
      this.collectionSize = 1;
      this.keyItemCategory = "";
      this.selectedItemCategory = new ItemCategory();
      this.loading = false;
    });
  }

  loadBrands() {
    this.brandService.findByName(this.keyBrand).subscribe((data: Array<Brand>) => {
      return this.brands = data;
    });
  }

  setSelectedBrand(event) {
    this.loading = true;
    this.stockService.findStockByBrandAndWh(event.item.code, this.selectedWarehouse.code).subscribe((data: Array<Stock>) => {
      this.subStocks = data;
      this.collectionSize = 1;
      this.keyBrand = "";
      this.selectedBrand = new Brand();
      this.loading = false;
    });
  }

  selectStockRecord(stock, index) {
    this.selectedStock = stock;
    this.selectedRow = index;
  }

  loadItems() {
    return this.itemService.findAllActiveByNameLike(this.keyItemSearch).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  loadItemByCode() {
    return this.itemService.findAllByBarcodeLike(this.barcode).subscribe((data: Array<Item>) => {
      return this.itemSearched = data;
    });
  }

  setSelectedItem(event) {
    this.selectedStock = event.item;
    this.searchSubStock();
  }

  clearTable() {
    this.subStocks = [];
  }

  filterByWarehouse() {
    this.subStocks = [];
    this.stockService.findAllByWarehouse(this.selectedWarehouse.code, this.page - 1, this.pageSize).subscribe((result: any) => {
      this.subStocks = result.content;
      this.collectionSize = result.totalPages * 10;
      this.keyItemSearch = "";
      this.selectedStock = new Stock();
    });
  }

  searchSubStock() {
    this.subStocks = [];
    this.stockService.findByBarcodeAndWarehouse(this.selectedStock.barcode, this.selectedWarehouse.code)
      .subscribe((data: Array<Stock>) => {
        this.subStocks = data;
        this.keyItemSearch = "";
        this.selectedStock = new Stock();
      });
  }

  /**
   * Search for stock by barcode
   * Used when opening the modal with a specific item barcode
   */
  searchStockByBarcode() {
    console.log('Searching for stock by barcode:', this.barcode);
    this.loading = true;
    this.subStocks = [];

    if (!this.barcode) {
      console.error('No barcode provided for stock search');
      this.loading = false;
      return;
    }

    if (!this.selectedWarehouse || !this.selectedWarehouse.code) {
      console.error('No warehouse selected for stock search');
      // Try to use a default warehouse code
      this.selectedWarehouse = new Warehouse();
      this.selectedWarehouse.code = 0;
      this.selectedWarehouse.name = 'Default Warehouse';
      console.log('Using default warehouse code 0');
    }

    console.log('Searching for stock with barcode:', this.barcode, 'in warehouse:', this.selectedWarehouse.code);

    this.stockService.findByBarcodeAndWarehouse(this.barcode, this.selectedWarehouse.code)
      .subscribe(
        (data: Array<Stock>) => {
          console.log('Stock data received:', data);
          this.subStocks = data || [];
          this.loading = false;

          // If we found stock records, select the first one
          if (this.subStocks && this.subStocks.length > 0) {
            console.log('Selecting first stock record');
            this.selectStockRecord(this.subStocks[0], 0);
          } else {
            console.log('No stock records found for barcode:', this.barcode);
          }
        },
        error => {
          console.error('Error loading stock by barcode:', error);
          this.loading = false;
        }
      );
  }

  adjustStock() {
    this.modalRef = this.modalService.show(AdjustStockComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true
      }
    });
    this.modalRef.content.stock = this.selectedStock;
    this.modalRef.content.modalRef = this.modalRef;
  }

  transferStock() {
    this.modalRef = this.modalService.show(TransferStockComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true
      }
    });
    this.modalRef.content.availableQtySource = this.selectedStock.quantity;
    this.modalRef.content.transferStock.sourceWarehouseCode = this.selectedWarehouse.code;
    this.modalRef.content.transferStock.itemCode = this.selectedStock.itemCode;
    this.modalRef.content.transferStock.itemName = this.selectedStock.itemName;
    this.modalRef.content.transferStock.barcode = this.selectedStock.barcode;
    this.modalRef.content.transferStock.sellingPrice = this.selectedStock.sellingPrice;
    this.modalRef.content.keyBarcodeSearch = this.selectedStock.barcode;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.filterByWarehouse();
    })
  }

}
