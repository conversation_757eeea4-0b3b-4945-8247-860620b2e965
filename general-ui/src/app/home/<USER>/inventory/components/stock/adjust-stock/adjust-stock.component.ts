import {Component, OnInit} from '@angular/core';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {StockService} from '../../../service/stock.service';
import {ItemService} from '../../../service/item.service';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Stock} from "../../../model/stock";
import {Item} from "../../../model/item";

@Component({
  standalone: false,
  selector: 'app-adjust-main-stock',
  templateUrl: './adjust-stock.component.html',
  styleUrls: ['./adjust-stock.component.css']
})
export class AdjustStockComponent implements OnInit {

  stock: Stock;

  actualQuantity: number;
  remark: string;
  modalRef: BsModalRef;

  constructor(
    private notificationService: NotificationService,
    private stockService: StockService,
    private itemService: ItemService
  ) {
  }

  ngOnInit(): void {
    this.stock = new Stock();
  }

  adjustStock(form: NgForm) {
    // First check if the item has manageSerial flag
    this.itemService.findOneByBarcode(this.stock.barcode).subscribe((item: Item) => {
      if (item && item) {

        // Check if the item has manageSerial flag
        if (item.manageSerial) {
          this.notificationService.showError('Items with serial number management cannot have their quantity adjusted directly. Please use the Purchase Invoice (PI) component to add or remove serial numbers.');
          return;
        }

        // If item doesn't have manageSerial flag, proceed with the adjustment
        // TODO ; Search updated stock to show updated result
        this.stockService.adjustStock(this.actualQuantity, this.stock.id, this.remark).subscribe((result: any) => {
          if (result.code === 200) {
            this.notificationService.showSuccess(result.message);
            form.reset();
            this.modalRef.hide();
          } else {
            this.notificationService.showError(result.message);
            console.log(result.data)
          }
        });
      } else {
        this.notificationService.showError('Item not found. Please try again.');
      }
    }, error => {
      this.notificationService.showError('Error checking item details. Please try again.');
      console.error(error);
    });
  }

}
