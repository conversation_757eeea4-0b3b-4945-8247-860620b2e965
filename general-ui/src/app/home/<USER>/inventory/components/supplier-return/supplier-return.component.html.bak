<div class="container-fluid">
  <h2 class="text-left mb-4">Supplier Return</h2>

  <!-- Item Selection Section -->
  <div class="row mb-3">

    <!-- Supplier Selection Section -->
    <div class="col-md-4">
      <label for="supplierSearch">Select Supplier</label>
      <input type="text"
             id="supplierSearch"
             class="form-control"
             [(ngModel)]="keySupplier"
             [typeahead]="supplierSearchResults"
             (typeaheadLoading)="loadSuppliers()"
             (typeaheadOnSelect)="setSelectedSupplier($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="name"
             placeholder="Search and select supplier..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>

    <div class="col-md-4">
      <label for="barcodeSearch">Search by Barcode</label>
      <input type="text"
             id="barcodeSearch"
             class="form-control"
             [(ngModel)]="keyBarcode"
             [typeahead]="itemSearchResults"
             (typeaheadLoading)="loadItemsByBarcode()"
             (typeaheadOnSelect)="selectItem($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="barcode"
             placeholder="Enter or scan barcode..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
    <div class="col-md-4">
      <label for="itemNameSearch">Search by Item Name</label>
      <input type="text"
             id="itemNameSearch"
             class="form-control"
             [(ngModel)]="keyItemName"
             [typeahead]="itemNameSearchResults"
             (typeaheadLoading)="loadItemsByName()"
             (typeaheadOnSelect)="selectItem($event)"
             [typeaheadOptionsLimit]="15"
             typeaheadWaitMs="500"
             typeaheadOptionField="itemName"
             placeholder="Type item name..."
             autocomplete="off"
             [ngModelOptions]="{standalone: true}">
    </div>
  </div>

  <!-- Item Details Section (shown when item is selected) -->
  <div class="row mb-3" *ngIf="selectedItem">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">Add Item to Return: {{ selectedItem.itemName }}</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-3">
              <label for="priceSelect">Select Price</label>
              <select id="priceSelect"
                      class="form-control"
                      [(ngModel)]="selectedPrice"
                      (ngModelChange)="onPriceChange()"
                      [ngModelOptions]="{standalone: true}">
                <option value="">Select Price</option>
                <option *ngFor="let price of availablePrices" [value]="price.sellingPrice">
                  {{ price.sellingPrice | number:'1.2-2' }} LKR (Stock: {{ price.quantity | number:'1.2-2' }})
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <label for="quantityInput">Quantity</label>
              <input type="number"
                     id="quantityInput"
                     class="form-control"
                     [(ngModel)]="returnQuantity"
                     min="0.01"
                     step="0.01"
                     [max]="currentStock"
                     placeholder="0.00"
                     [ngModelOptions]="{standalone: true}">
            </div>
            <div class="col-md-3">
              <label for="reasonSelect">Return Reason</label>
              <select id="reasonSelect"
                      class="form-control"
                      [(ngModel)]="selectedReason"
                      [ngModelOptions]="{standalone: true}">
                <option value="">Select Reason</option>
                <option *ngFor="let reason of returnReasons" [value]="reason">{{ reason }}</option>
              </select>
            </div>
            <div class="col-md-2">
              <label>&nbsp;</label>
              <div>
                <button type="button"
                        class="btn btn-theme btn-block"
                        (click)="addItemToReturnList()"
                        [disabled]="!canAddItem()">
                  <i class="fa fa-plus"></i> Add
                </button>
              </div>
            </div>
            <div class="col-md-2">
              <label>&nbsp;</label>
              <div>
                <button type="button"
                        class="btn btn-secondary btn-block"
                        (click)="clearSelection()">
                  <i class="fa fa-times"></i> Clear
                </button>
              </div>
            </div>
          </div>
          <div class="row mt-2" *ngIf="selectedPrice">
            <div class="col-12">
              <small class="text-muted">
                <strong>Current Stock:</strong> {{ currentStock | number:'1.2-2' }} |
                <strong>Item Cost:</strong> {{ currentItemCost | number:'1.2-2' }} LKR |
                <strong>Total Cost:</strong> {{ (returnQuantity * currentItemCost) | number:'1.2-2' }} LKR
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Supplier Display -->
  <div class="row mb-3" *ngIf="selectedSupplier">
    <div class="col-12">
      <div class="alert alert-info">
        <strong>Selected Supplier:</strong> {{ selectedSupplier.name }}
        <button type="button" class="btn btn-sm btn-outline-secondary ml-2" (click)="clearSelectedSupplier()">
          <i class="fa fa-times"></i> Change
        </button>
      </div>
    </div>
  </div>

  <!-- Return Items Table -->
  <div class="row mb-3">
    <div class="col-12">
      <h5>Items to Return</h5>
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-light">
          <tr class="text-center">
            <th>Item Code</th>
            <th>Item Name</th>
            <th>Barcode</th>
            <th>Current Stock</th>
            <th>Selling Price (LKR)</th>
            <th>Return Qty</th>
            <th>Unit Cost (LKR)</th>
            <th>Total Cost (LKR)</th>
            <th>Reason</th>
            <th>Actions</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let item of returnItems; let i = index" class="text-center">
            <td>{{ item.itemCode }}</td>
            <td class="text-left">{{ item.itemName }}</td>
            <td>{{ item.barcode }}</td>
            <td>{{ item.currentStock | number:'1.2-2' }}</td>
            <td>{{ item.sellingPrice | number:'1.2-2' }}</td>
            <td>
              <input type="number"
                     class="form-control form-control-sm"
                     [(ngModel)]="item.returnQuantity"
                     (ngModelChange)="updateItemTotal(i)"
                     min="0.01"
                     step="0.01"
                     style="width: 100px;"
                     [ngModelOptions]="{standalone: true}">
            </td>
            <td>{{ item.itemCost | number:'1.2-2' }}</td>
            <td>{{ item.totalCost | number:'1.2-2' }}</td>
            <td>
              <select class="form-control form-control-sm"
                      [(ngModel)]="item.reason"
                      style="width: 150px;"
                      [ngModelOptions]="{standalone: true}">
                <option value="">Select reason</option>
                <option *ngFor="let reason of returnReasons" [value]="reason">{{ reason }}</option>
              </select>
            </td>
            <td>
              <button type="button"
                      class="btn btn-sm btn-outline-danger"
                      (click)="removeItemFromReturn(i)"
                      title="Remove item">
                <i class="fa fa-trash"></i>
              </button>
            </td>
          </tr>
          <tr *ngIf="returnItems.length === 0">
            <td colspan="10" class="text-center text-muted py-4">
              <i class="fa fa-inbox fa-2x mb-2"></i>
              <br>
              No items added for return. Search and select items above to add them.
            </td>
          </tr>
          </tbody>
          <tfoot class="table-light font-weight-bold" *ngIf="returnItems.length > 0">
          <tr class="text-center">
            <td colspan="5" class="text-right">Totals:</td>
            <td>{{ getTotalQuantity() | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ getTotalCost() | number:'1.2-2' }}</td>
            <td colspan="2"></td>
          </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </div>

  <!-- Validation Messages -->
  <div class="row mb-3" *ngIf="returnItems.length > 0 && !selectedSupplier">
    <div class="col-12">
      <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i>
        Please select a supplier before processing the return.
      </div>
    </div>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="text-center py-4">
    <div class="spinner-border text-primary" role="status">
      <span class="sr-only">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Processing return...</p>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Items: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ returnItems.length }}</span>
                <span class="font-weight-bold mb-0 ml-3">Total Quantity: </span>
                <span class="font-weight-bold mb-0 text-info">{{ getTotalQuantity() | number:'1.2-2' }}</span>
                <span class="font-weight-bold mb-0 ml-3">Total Cost: </span>
                <span class="font-weight-bold mb-0 text-success">{{ getTotalCost() | number:'1.2-2' }} LKR</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button"
                  class="btn btn-secondary mr-2"
                  (click)="clearAllItems()"
                  [disabled]="returnItems.length === 0">
            <i class="fa fa-times"></i> Clear All
          </button>
          <button type="button"
                  class="btn btn-danger"
                  (click)="processReturn()"
                  [disabled]="!selectedSupplier || !isValidReturn() || loading">
            <i class="fa fa-undo"></i> Process Return
          </button>
        </div>
      </div>
    </div>
  </div>

  <ngx-spinner type="ball-scale-multiple"></ngx-spinner>
</div>
