import { Component, OnInit } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ItemService } from '../../../service/item.service';
import { NotificationService } from '../../../../../core/service/notification.service';
import { Item } from '../../../model/item';

@Component({
  standalone: false,
  selector: 'app-edit-barcode',
  templateUrl: './edit-barcode.component.html',
  styleUrls: ['./edit-barcode.component.css']
})
export class EditBarcodeComponent implements OnInit {
  // Item data passed from parent component
  item: Item;

  // New barcode value
  newBarcode: string = '';

  // Loading state
  loading: boolean = false;

  constructor(
    public modalRef: BsModalRef,
    private itemService: ItemService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    // Initialize new barcode with current value
    if (this.item && this.item.barcode) {
      this.newBarcode = this.item.barcode;
    }
  }

  /**
   * Update the barcode
   */
  updateBarcode(): void {
    // Validate input
    if (!this.newBarcode || this.newBarcode.trim() === '') {
      this.notificationService.showError('Barcode cannot be empty');
      return;
    }

    // Check if barcode has changed
    if (this.newBarcode === this.item.barcode) {
      this.notificationService.showWarning('Barcode has not changed');
      return;
    }

    this.loading = true;

    // Call service to update barcode
    this.itemService.updateBarcode(this.item.id, this.item.barcode, this.newBarcode)
      .subscribe(
        (result) => {
          this.loading = false;
          if (result.code === 200) {
            this.notificationService.showSuccess(result.message || 'Barcode updated successfully');
            // Close modal
            this.modalRef.hide();
          } else {
            this.notificationService.showError(result.message || 'Failed to update barcode');
          }
        },
        (error) => {
          this.loading = false;
          this.notificationService.showError('Error updating barcode: ' + (error.message || 'Unknown error'));
        }
      );
  }

  /**
   * Close the modal
   */
  close(): void {
    this.modalRef.hide();
  }
}
