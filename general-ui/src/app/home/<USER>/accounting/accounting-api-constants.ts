import {environment} from '../../../../environments/environment';

export class AccountingApiConstants {

  public static API_URL = environment.apiUrl;

  // Bank Account Management API endpoints
  public static BANK_ACCOUNT_SAVE = AccountingApiConstants.API_URL + 'bankAccount/save';
  public static BANK_ACCOUNT_FIND_ALL = AccountingApiConstants.API_URL + 'bankAccount/findAllBankAccounts';
  public static BANK_ACCOUNT_FIND_BY_ID = AccountingApiConstants.API_URL + 'bankAccount/findById';
  public static BANK_ACCOUNT_FIND_BY_ACCOUNT_NO = AccountingApiConstants.API_URL + 'bankAccount/search';
  public static BANK_ACCOUNT_FIND_ALL_PAGE = AccountingApiConstants.API_URL + 'bankAccount/findAllPage';
  public static BANK_ACCOUNT_FIND_BY_BANK_NAME = AccountingApiConstants.API_URL + 'bankAccount/findByBankName';
  public static BANK_ACCOUNT_FIND_BY_ACCOUNT_HOLDER_NAME = AccountingApiConstants.API_URL + 'bankAccount/findByAccountHolderName';
  public static BANK_ACCOUNT_FIND_BY_ACCOUNT_TYPE = AccountingApiConstants.API_URL + 'bankAccount/findByAccountType';
  public static BANK_ACCOUNT_GET_CURRENT_BALANCE = AccountingApiConstants.API_URL + 'bankAccount/getCurrentBalance';
  public static BANK_ACCOUNT_UPDATE_BALANCE = AccountingApiConstants.API_URL + 'bankAccount/updateBalance';
  public static BANK_ACCOUNT_CORRECT_BALANCE = AccountingApiConstants.API_URL + 'bankAccount/correctBalance';

  // Bank Account Transaction API endpoints
  public static BANK_TRANSACTION_CREATE = AccountingApiConstants.API_URL + 'bankAccount/createTransaction';
  public static BANK_TRANSACTION_FIND_BY_ACCOUNT = AccountingApiConstants.API_URL + 'bankAccount/findTransactionsByBankAccount';
  public static BANK_TRANSACTION_FIND_BY_REFERENCE = AccountingApiConstants.API_URL + 'bankAccount/findTransactionsByReferenceNo';
  public static BANK_TRANSACTION_FIND_BY_DATE_RANGE = AccountingApiConstants.API_URL + 'bankAccount/findTransactionsByDateRange';
  public static BANK_TRANSACTION_FIND_PAGE = AccountingApiConstants.API_URL + 'bankAccount/findTransactionsPage';

  // Petty Cash Management API endpoints
  public static PETTY_CASH_SAVE = AccountingApiConstants.API_URL + 'pettyCash/save';
  public static PETTY_CASH_FIND_ALL_ACTIVE = AccountingApiConstants.API_URL + 'pettyCash/findAllActive';
  public static PETTY_CASH_FIND_BY_NO = AccountingApiConstants.API_URL + 'pettyCash/findByPettyCashNo';
  public static PETTY_CASH_FIND_BY_ID = AccountingApiConstants.API_URL + 'pettyCash/findById';
  public static PETTY_CASH_FIND_BY_LOCATION = AccountingApiConstants.API_URL + 'pettyCash/findByLocation';
  public static PETTY_CASH_FIND_BY_CUSTODIAN = AccountingApiConstants.API_URL + 'pettyCash/findByCustodian';

  // Petty Cash Transaction API endpoints
  public static PETTY_CASH_CREATE_TRANSACTION = AccountingApiConstants.API_URL + 'pettyCash/createTransaction';
  public static PETTY_CASH_REPLENISH = AccountingApiConstants.API_URL + 'pettyCash/replenish';
  public static PETTY_CASH_RECORD_EXPENSE = AccountingApiConstants.API_URL + 'pettyCash/recordExpense';
  public static PETTY_CASH_RECORD_REFUND = AccountingApiConstants.API_URL + 'pettyCash/recordRefund';

  // Petty Cash Transaction Query API endpoints
  public static PETTY_CASH_FIND_TRANSACTIONS = AccountingApiConstants.API_URL + 'pettyCash/findTransactionsByPettyCash';
  public static PETTY_CASH_FIND_TRANSACTIONS_PAGED = AccountingApiConstants.API_URL + 'pettyCash/findTransactionsByPettyCashPaged';
  public static PETTY_CASH_FIND_TRANSACTIONS_BY_DATE_RANGE = AccountingApiConstants.API_URL + 'pettyCash/findTransactionsByDateRange';
  public static PETTY_CASH_FIND_TRANSACTIONS_BY_TYPE = AccountingApiConstants.API_URL + 'pettyCash/findTransactionsByType';
  public static PETTY_CASH_FIND_TRANSACTIONS_BY_EXPENSE_CATEGORY = AccountingApiConstants.API_URL + 'pettyCash/findTransactionsByExpenseCategory';
  public static PETTY_CASH_FIND_TRANSACTIONS_BY_OPERATOR = AccountingApiConstants.API_URL + 'pettyCash/findTransactionsByOperator';
  public static PETTY_CASH_FIND_ALL_TRANSACTIONS_BY_DATE_RANGE = AccountingApiConstants.API_URL + 'pettyCash/findAllTransactionsByDateRange';
  public static PETTY_CASH_FIND_TRANSACTION_BY_VOUCHER_NO = AccountingApiConstants.API_URL + 'pettyCash/findTransactionByVoucherNo';

  // Petty Cash Balance Management API endpoints
  public static PETTY_CASH_GET_CURRENT_BALANCE = AccountingApiConstants.API_URL + 'pettyCash/getCurrentBalance';
  public static PETTY_CASH_UPDATE_BALANCE = AccountingApiConstants.API_URL + 'pettyCash/updateBalance';
  public static PETTY_CASH_VALIDATE_SUFFICIENT_BALANCE = AccountingApiConstants.API_URL + 'pettyCash/validateSufficientBalance';

  // Accounts Receivable API endpoints
  public static AR_OUTSTANDING_INVOICES = AccountingApiConstants.API_URL + 'accountsReceivable/outstandingInvoices';
  public static AR_OUTSTANDING_BY_CUSTOMER = AccountingApiConstants.API_URL + 'accountsReceivable/outstandingInvoicesByCustomer';
  public static AR_CUSTOMER_BALANCES = AccountingApiConstants.API_URL + 'accountsReceivable/customerOutstandingBalances';
  public static AR_AGING_ANALYSIS = AccountingApiConstants.API_URL + 'accountsReceivable/agingAnalysis';
  public static AR_TOTAL = AccountingApiConstants.API_URL + 'accountsReceivable/totalAccountsReceivable';
  public static AR_OVERDUE_INVOICES = AccountingApiConstants.API_URL + 'accountsReceivable/overdueInvoices';
  public static AR_TOP_CUSTOMERS = AccountingApiConstants.API_URL + 'accountsReceivable/topCustomersByOutstanding';

  // Accounts Payable API endpoints
  public static AP_OUTSTANDING_INVOICES = AccountingApiConstants.API_URL + 'accountsPayable/outstandingInvoices';
  public static AP_OUTSTANDING_BY_SUPPLIER = AccountingApiConstants.API_URL + 'accountsPayable/outstandingInvoicesBySupplier';
  public static AP_SUPPLIER_BALANCES = AccountingApiConstants.API_URL + 'accountsPayable/supplierOutstandingBalances';
  public static AP_AGING_ANALYSIS = AccountingApiConstants.API_URL + 'accountsPayable/agingAnalysis';
  public static AP_TOTAL = AccountingApiConstants.API_URL + 'accountsPayable/totalAccountsPayable';
  public static AP_OVERDUE_INVOICES = AccountingApiConstants.API_URL + 'accountsPayable/overdueInvoices';
  public static AP_TOP_SUPPLIERS = AccountingApiConstants.API_URL + 'accountsPayable/topSuppliersByOutstanding';
  public static AP_PAYMENT_SCHEDULE = AccountingApiConstants.API_URL + 'accountsPayable/paymentSchedule';

  // ASSETS
  public static SAVE_ASSET = AccountingApiConstants.API_URL + 'asset/save';
  public static FIND_ALL_ASSETS = AccountingApiConstants.API_URL + 'asset/findAll';
  public static FIND_ASSET_BY_ID = AccountingApiConstants.API_URL + 'asset/findById';
  public static FIND_ASSETS_BY_TYPE = AccountingApiConstants.API_URL + 'asset/findByType';
  public static FIND_ASSETS_BY_CATEGORY = AccountingApiConstants.API_URL + 'asset/findByCategory';
  public static FIND_ASSETS_BY_STATUS = AccountingApiConstants.API_URL + 'asset/findByStatus';
  public static CALCULATE_ASSET_DEPRECIATION = AccountingApiConstants.API_URL + 'asset/calculateDepreciation';
  public static GET_TOTAL_ASSET_VALUE = AccountingApiConstants.API_URL + 'asset/getTotalValue';
  public static GET_ASSETS_BY_TYPE_VALUE = AccountingApiConstants.API_URL + 'asset/getByTypeValue';
  public static DISPOSE_ASSET = AccountingApiConstants.API_URL + 'asset/dispose';

  // LIABILITIES
  public static SAVE_LIABILITY = AccountingApiConstants.API_URL + 'liability/save';
  public static FIND_ALL_LIABILITIES = AccountingApiConstants.API_URL + 'liability/findAll';
  public static FIND_LIABILITY_BY_ID = AccountingApiConstants.API_URL + 'liability/findById';
  public static FIND_LIABILITIES_BY_TYPE = AccountingApiConstants.API_URL + 'liability/findByType';
  public static FIND_LIABILITIES_BY_STATUS = AccountingApiConstants.API_URL + 'liability/findByStatus';
  public static FIND_OVERDUE_LIABILITIES = AccountingApiConstants.API_URL + 'liability/findOverdue';
  public static MAKE_LIABILITY_PAYMENT = AccountingApiConstants.API_URL + 'liability/makePayment';
  public static GET_TOTAL_LIABILITY_VALUE = AccountingApiConstants.API_URL + 'liability/getTotalValue';
  public static GET_LIABILITIES_BY_TYPE_VALUE = AccountingApiConstants.API_URL + 'liability/getByTypeValue';
  public static CALCULATE_LIABILITY_INTEREST = AccountingApiConstants.API_URL + 'liability/calculateInterest';
  public static GET_UPCOMING_PAYMENTS = AccountingApiConstants.API_URL + 'liability/getUpcomingPayments';

  // EQUITY
  public static SAVE_EQUITY = AccountingApiConstants.API_URL + 'equity/save';
  public static FIND_ALL_EQUITY = AccountingApiConstants.API_URL + 'equity/findAll';
  public static FIND_EQUITY_BY_ID = AccountingApiConstants.API_URL + 'equity/findById';
  public static FIND_EQUITY_BY_TYPE = AccountingApiConstants.API_URL + 'equity/findByType';
  public static FIND_EQUITY_BY_FISCAL_YEAR = AccountingApiConstants.API_URL + 'equity/findByFiscalYear';
  public static FIND_EQUITY_BY_OWNER = AccountingApiConstants.API_URL + 'equity/findByOwner';
  public static GET_TOTAL_EQUITY_VALUE = AccountingApiConstants.API_URL + 'equity/getTotalValue';
  public static GET_EQUITY_BY_TYPE_VALUE = AccountingApiConstants.API_URL + 'equity/getByTypeValue';
  public static GET_RETAINED_EARNINGS = AccountingApiConstants.API_URL + 'equity/getRetainedEarnings';
  public static GET_TOTAL_CAPITAL = AccountingApiConstants.API_URL + 'equity/getTotalCapital';
  public static RECORD_PROFIT_RETENTION = AccountingApiConstants.API_URL + 'equity/recordProfitRetention';
  public static RECORD_OWNER_DRAWING = AccountingApiConstants.API_URL + 'equity/recordOwnerDrawing';


  //--------------------------------------------------

  // Account Management
  public static SAVE_ACCOUNT = AccountingApiConstants.API_URL + 'account/save';
  public static GET_ALL_ACCOUNTS = AccountingApiConstants.API_URL + 'account/findAllAccounts';
  public static SEARCH_ACCOUNT = AccountingApiConstants.API_URL + 'account/findById';
  public static GET_PAGES = AccountingApiConstants.API_URL + 'account/findAllPage';
  public static SEARCH_ACCOUNT_NO = AccountingApiConstants.API_URL + 'account/search';
  public static FIND_BY_ACCOUNT_NAME = AccountingApiConstants.API_URL + 'account/findByAccountName';
  public static FIND_BY_BANK_NAME = AccountingApiConstants.API_URL + 'account/findByBankName';
  public static FIND_BY_ACCOUNT_HOLDER_NAME = AccountingApiConstants.API_URL + 'account/findByAccountHolderName';
  public static FIND_BY_ACCOUNT_TYPE = AccountingApiConstants.API_URL + 'account/findByAccountType';
  public static FIND_BY_SUPPLIER_NAME = AccountingApiConstants.API_URL + 'account/findBySupplierName';
  public static FIND_BY_CUSTOMER_NAME = AccountingApiConstants.API_URL + 'account/findByCustomerName';

  // Account Transaction Management
  public static CREATE_ACCOUNT_TRANSACTION = AccountingApiConstants.API_URL + 'account/createTransaction';
  public static FIND_TRANSACTIONS_BY_ACCOUNT = AccountingApiConstants.API_URL + 'account/findTransactionsByAccount';
  public static FIND_TRANSACTIONS_BY_REFERENCE_NO = AccountingApiConstants.API_URL + 'account/findTransactionsByReferenceNo';
  public static FIND_TRANSACTIONS_BY_DATE_RANGE = AccountingApiConstants.API_URL + 'account/findTransactionsByDateRange';
  public static FIND_TRANSACTIONS_BY_ACCOUNT_AND_DATE_RANGE = AccountingApiConstants.API_URL + 'account/findTransactionsByAccountAndDateRange';
  public static FIND_TRANSACTIONS_PAGE = AccountingApiConstants.API_URL + 'account/findTransactionsPage';

  // Account Balance Management
  public static GET_CURRENT_BALANCE = AccountingApiConstants.API_URL + 'account/getCurrentBalance';
  public static UPDATE_BALANCE = AccountingApiConstants.API_URL + 'account/updateBalance';
  public static DEBIT_ACCOUNT = AccountingApiConstants.API_URL + 'account/debitAccount';
  public static CREDIT_ACCOUNT = AccountingApiConstants.API_URL + 'account/creditAccount';
  public static TRANSFER_FUNDS = AccountingApiConstants.API_URL + 'account/transferFunds';
  public static PROCESS_PURCHASE_PAYMENT = AccountingApiConstants.API_URL + 'account/processPurchasePayment';

  // Account Utility
  public static GET_LAST_TRANSACTION = AccountingApiConstants.API_URL + 'account/getLastTransaction';
  public static CALCULATE_BALANCE_FROM_TRANSACTIONS = AccountingApiConstants.API_URL + 'account/calculateBalanceFromTransactions';
  public static VALIDATE_SUFFICIENT_BALANCE = AccountingApiConstants.API_URL + 'account/validateSufficientBalance';
}
