import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AccountingApiConstants } from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class AccountsPayableService {

  constructor(private http: HttpClient) { }

  getOutstandingInvoices(): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AP_OUTSTANDING_INVOICES);
  }

  getOutstandingInvoicesBySupplier(supplierId: string): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AP_OUTSTANDING_BY_SUPPLIER, {
      params: { supplierId: supplierId }
    });
  }

  getSupplierOutstandingBalances(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.AP_SUPPLIER_BALANCES);
  }

  getAgingAnalysis(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.AP_AGING_ANALYSIS);
  }

  getTotalAccountsPayable(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.AP_TOTAL);
  }

  getOverdueInvoices(): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AP_OVERDUE_INVOICES);
  }

  getTopSuppliersByOutstanding(limit: number = 10): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AP_TOP_SUPPLIERS, {
      params: { limit: limit.toString() }
    });
  }

  getPaymentSchedule(daysAhead: number = 30): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AP_PAYMENT_SCHEDULE, {
      params: { daysAhead: daysAhead.toString() }
    });
  }
}
