import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';
import {PettyCash} from '../model/petty-cash';
import {PettyCashTransaction} from '../model/petty-cash-transaction';
import {Response} from '../../../core/model/response';
import {AccountingApiConstants} from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class PettyCashService {

  constructor(private http: HttpClient) { }

  // Petty Cash Management
  save(pettyCash: PettyCash): Observable<Response> {
    return this.http.post<Response>(AccountingApiConstants.PETTY_CASH_SAVE, pettyCash);
  }

  findAllActive(): Observable<PettyCash[]> {
    return this.http.get<PettyCash[]>(AccountingApiConstants.PETTY_CASH_FIND_ALL_ACTIVE);
  }

  findByPettyCashNo(pettyCashNo: string): Observable<PettyCash> {
    return this.http.get<PettyCash>(AccountingApiConstants.PETTY_CASH_FIND_BY_NO, {
      params: { pettyCashNo: pettyCashNo }
    });
  }

  findById(id: string): Observable<PettyCash> {
    return this.http.get<PettyCash>(AccountingApiConstants.PETTY_CASH_FIND_BY_ID, {
      params: { id: id }
    });
  }

  findByLocation(location: string): Observable<PettyCash[]> {
    return this.http.get<PettyCash[]>(AccountingApiConstants.PETTY_CASH_FIND_BY_LOCATION, {
      params: { location: location }
    });
  }

  findByCustodian(custodian: string): Observable<PettyCash[]> {
    return this.http.get<PettyCash[]>(AccountingApiConstants.PETTY_CASH_FIND_BY_CUSTODIAN, {
      params: { custodian: custodian }
    });
  }

  // Transaction Management
  createTransaction(transaction: PettyCashTransaction): Observable<Response> {
    return this.http.post<Response>(AccountingApiConstants.PETTY_CASH_CREATE_TRANSACTION, transaction);
  }

  replenishPettyCash(pettyCashId: string, amount: number, description: string, voucherNo?: string): Observable<Response> {
    const params: any = {
      pettyCashId: pettyCashId,
      amount: amount.toString(),
      description: description
    };
    if (voucherNo) {
      params.voucherNo = voucherNo;
    }
    return this.http.post<Response>(AccountingApiConstants.PETTY_CASH_REPLENISH, null, { params: params });
  }

  recordExpense(pettyCashId: string, amount: number, description: string, expenseCategoryId: string,
                voucherNo?: string, receivedBy?: string): Observable<Response> {
    const params: any = {
      pettyCashId: pettyCashId,
      amount: amount.toString(),
      description: description,
      expenseCategoryId: expenseCategoryId
    };
    if (voucherNo) {
      params.voucherNo = voucherNo;
    }
    if (receivedBy) {
      params.receivedBy = receivedBy;
    }
    return this.http.post<Response>(AccountingApiConstants.PETTY_CASH_RECORD_EXPENSE, null, { params: params });
  }

  recordRefund(pettyCashId: string, amount: number, description: string, voucherNo?: string): Observable<Response> {
    const params: any = {
      pettyCashId: pettyCashId,
      amount: amount.toString(),
      description: description
    };
    if (voucherNo) {
      params.voucherNo = voucherNo;
    }
    return this.http.post<Response>(AccountingApiConstants.PETTY_CASH_RECORD_REFUND, null, { params: params });
  }

  // Transaction Queries
  findTransactionsByPettyCash(pettyCashId: string): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTIONS, {
      params: { pettyCashId: pettyCashId }
    });
  }

  findTransactionsByPettyCashPaged(pettyCashId: string, page: number = 0, pageSize: number = 10): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTIONS_PAGED, {
      params: {
        pettyCashId: pettyCashId,
        page: page.toString(),
        pageSize: pageSize.toString()
      }
    });
  }

  findTransactionsByDateRange(pettyCashId: string, startDate: string, endDate: string): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTIONS_BY_DATE_RANGE, {
      params: {
        pettyCashId: pettyCashId,
        startDate: startDate,
        endDate: endDate
      }
    });
  }

  findTransactionsByType(pettyCashId: string, transactionTypeId: string): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTIONS_BY_TYPE, {
      params: {
        pettyCashId: pettyCashId,
        transactionTypeId: transactionTypeId
      }
    });
  }

  findTransactionsByExpenseCategory(pettyCashId: string, expenseCategoryId: string): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTIONS_BY_EXPENSE_CATEGORY, {
      params: {
        pettyCashId: pettyCashId,
        expenseCategoryId: expenseCategoryId
      }
    });
  }

  findTransactionsByOperator(pettyCashId: string, operator: string): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTIONS_BY_OPERATOR, {
      params: {
        pettyCashId: pettyCashId,
        operator: operator
      }
    });
  }

  findAllTransactionsByDateRange(startDate: string, endDate: string): Observable<PettyCashTransaction[]> {
    return this.http.get<PettyCashTransaction[]>(AccountingApiConstants.PETTY_CASH_FIND_ALL_TRANSACTIONS_BY_DATE_RANGE, {
      params: {
        startDate: startDate,
        endDate: endDate
      }
    });
  }

  findTransactionByVoucherNo(voucherNo: string): Observable<PettyCashTransaction> {
    return this.http.get<PettyCashTransaction>(AccountingApiConstants.PETTY_CASH_FIND_TRANSACTION_BY_VOUCHER_NO, {
      params: { voucherNo: voucherNo }
    });
  }

  // Balance Management
  getCurrentBalance(pettyCashId: string): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.PETTY_CASH_GET_CURRENT_BALANCE, {
      params: { pettyCashId: pettyCashId }
    });
  }

  updateBalance(pettyCashId: string, amount: number, operator: string): Observable<boolean> {
    return this.http.post<boolean>(AccountingApiConstants.PETTY_CASH_UPDATE_BALANCE, null, {
      params: {
        pettyCashId: pettyCashId,
        amount: amount.toString(),
        operator: operator
      }
    });
  }

  validateSufficientBalance(pettyCashId: string, amount: number): Observable<boolean> {
    return this.http.get<boolean>(AccountingApiConstants.PETTY_CASH_VALIDATE_SUFFICIENT_BALANCE, {
      params: {
        pettyCashId: pettyCashId,
        amount: amount.toString()
      }
    });
  }
}
