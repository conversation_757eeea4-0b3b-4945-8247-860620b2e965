import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Liability } from '../model/liability';
import { AccountingApiConstants } from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class LiabilityService {

  constructor(private http: HttpClient) { }

  save(liability: Liability): Observable<any> {
    return this.http.post(AccountingApiConstants.SAVE_LIABILITY, liability);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(AccountingApiConstants.FIND_ALL_LIABILITIES, {
      params: { page: page.toString(), pageSize: pageSize.toString() }
    });
  }

  findById(id: string): Observable<Liability> {
    return this.http.get<Liability>(AccountingApiConstants.FIND_LIABILITY_BY_ID, {
      params: { id: id }
    });
  }

  findByType(liabilityType: string): Observable<Liability[]> {
    return this.http.get<Liability[]>(AccountingApiConstants.FIND_LIABILITIES_BY_TYPE, {
      params: { liabilityType: liabilityType }
    });
  }

  findByStatus(status: string): Observable<Liability[]> {
    return this.http.get<Liability[]>(AccountingApiConstants.FIND_LIABILITIES_BY_STATUS, {
      params: { status: status }
    });
  }

  findOverdueLiabilities(): Observable<Liability[]> {
    return this.http.get<Liability[]>(AccountingApiConstants.FIND_OVERDUE_LIABILITIES);
  }

  makePayment(id: string, paymentAmount: number, paymentDate: Date, notes: string): Observable<any> {
    return this.http.post(AccountingApiConstants.MAKE_LIABILITY_PAYMENT, {
      id: id,
      paymentAmount: paymentAmount,
      paymentDate: paymentDate,
      notes: notes
    });
  }

  getTotalLiabilityValue(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.GET_TOTAL_LIABILITY_VALUE);
  }

  getLiabilitiesByTypeValue(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.GET_LIABILITIES_BY_TYPE_VALUE);
  }

  calculateInterest(id: string): Observable<any> {
    return this.http.post(AccountingApiConstants.CALCULATE_LIABILITY_INTEREST, { id: id });
  }

  getUpcomingPayments(days: number): Observable<Liability[]> {
    return this.http.get<Liability[]>(AccountingApiConstants.GET_UPCOMING_PAYMENTS, {
      params: { days: days.toString() }
    });
  }
}
