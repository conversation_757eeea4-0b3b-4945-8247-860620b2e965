import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Account } from '../model/account';
import { AccountTransaction } from '../model/account-transaction';
import { Response } from '../../../core/model/response';
import { AccountingApiConstants } from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class AccountService {

  constructor(private http: HttpClient) { }

  // Account Management
  save(account: Account): Observable<Response> {
    return this.http.post<Response>(AccountingApiConstants.SAVE_ACCOUNT, account);
  }

  findAllAccounts(): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.GET_ALL_ACCOUNTS);
  }

  findAllActive(): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.GET_ALL_ACCOUNTS);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(AccountingApiConstants.GET_PAGES, {
      params: { page: page.toString(), pageSize: pageSize.toString() }
    });
  }

  findById(accountId: string): Observable<Account> {
    return this.http.get<Account>(AccountingApiConstants.SEARCH_ACCOUNT, {
      params: { id: accountId }
    });
  }

  findByAccountNo(accountNo: string): Observable<Account> {
    return this.http.get<Account>(AccountingApiConstants.SEARCH_ACCOUNT_NO, {
      params: { any: accountNo }
    });
  }

  findByAccountName(accountName: string): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.FIND_BY_ACCOUNT_NAME, {
      params: { accountName: accountName }
    });
  }

  findByBankName(bankName: string): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.FIND_BY_BANK_NAME, {
      params: { bankName: bankName }
    });
  }

  findByAccountHolderName(accountHolderName: string): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.FIND_BY_ACCOUNT_HOLDER_NAME, {
      params: { accountHolderName: accountHolderName }
    });
  }

  findByAccountType(accountType: string): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.FIND_BY_ACCOUNT_TYPE, {
      params: { accountType: accountType }
    });
  }

  findBySupplierName(supplierName: string): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.FIND_BY_SUPPLIER_NAME, {
      params: { supplierName: supplierName }
    });
  }

  findByCustomerName(customerName: string): Observable<Account[]> {
    return this.http.get<Account[]>(AccountingApiConstants.FIND_BY_CUSTOMER_NAME, {
      params: { customerName: customerName }
    });
  }

  // Account Transaction Management
  createTransaction(transaction: AccountTransaction): Observable<Response> {
    return this.http.post<Response>(AccountingApiConstants.CREATE_ACCOUNT_TRANSACTION, transaction);
  }

  findTransactionsByAccount(accountId: string): Observable<AccountTransaction[]> {
    return this.http.get<AccountTransaction[]>(AccountingApiConstants.FIND_TRANSACTIONS_BY_ACCOUNT, {
      params: { accountId: accountId }
    });
  }

  findTransactionsByReferenceNo(referenceNo: string): Observable<AccountTransaction[]> {
    return this.http.get<AccountTransaction[]>(AccountingApiConstants.FIND_TRANSACTIONS_BY_REFERENCE_NO, {
      params: { referenceNo: referenceNo }
    });
  }

  findTransactionsByDateRange(startDate: string, endDate: string): Observable<AccountTransaction[]> {
    return this.http.get<AccountTransaction[]>(AccountingApiConstants.FIND_TRANSACTIONS_BY_DATE_RANGE, {
      params: { startDate: startDate, endDate: endDate }
    });
  }

  findTransactionsByAccountAndDateRange(accountId: string, startDate: string, endDate: string): Observable<AccountTransaction[]> {
    return this.http.get<AccountTransaction[]>(AccountingApiConstants.FIND_TRANSACTIONS_BY_ACCOUNT_AND_DATE_RANGE, {
      params: { accountId: accountId, startDate: startDate, endDate: endDate }
    });
  }

  findTransactionsPage(accountId: string, page: number, pageSize: number): Observable<any> {
    return this.http.get(AccountingApiConstants.FIND_TRANSACTIONS_PAGE, {
      params: {
        accountId: accountId,
        page: page.toString(),
        pageSize: pageSize.toString()
      }
    });
  }

  // Balance Management
  getCurrentBalance(accountId: string): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.GET_CURRENT_BALANCE, {
      params: { accountId: accountId }
    });
  }

  updateBalance(accountId: string, amount: number, operator: string): Observable<boolean> {
    return this.http.post<boolean>(AccountingApiConstants.UPDATE_BALANCE, null, {
      params: {
        accountId: accountId,
        amount: amount.toString(),
        operator: operator
      }
    });
  }

  // Balance Correction (legacy method for bank account management component)
  correctBalance(accountId: string, newBalance: number, reason: string, correctedBy: string): Observable<Response> {
    return this.http.post<Response>(AccountingApiConstants.BANK_ACCOUNT_CORRECT_BALANCE, null, {
      params: {
        bankAccountId: accountId, // Note: using bankAccountId for backward compatibility
        newBalance: newBalance.toString(),
        reason: reason,
        correctedBy: correctedBy
      }
    });
  }

  debitAccount(accountId: string, amount: number, referenceNo: string, referenceType: string, description: string, thirdParty?: string): Observable<boolean> {
    const params: any = {
      accountId: accountId,
      amount: amount.toString(),
      referenceNo: referenceNo,
      referenceType: referenceType,
      description: description
    };
    if (thirdParty) {
      params.thirdParty = thirdParty;
    }
    return this.http.post<boolean>(AccountingApiConstants.DEBIT_ACCOUNT, null, { params: params });
  }

  creditAccount(accountId: string, amount: number, referenceNo: string, referenceType: string, description: string, thirdParty?: string): Observable<boolean> {
    const params: any = {
      accountId: accountId,
      amount: amount.toString(),
      referenceNo: referenceNo,
      referenceType: referenceType,
      description: description
    };
    if (thirdParty) {
      params.thirdParty = thirdParty;
    }
    return this.http.post<boolean>(AccountingApiConstants.CREDIT_ACCOUNT, null, { params: params });
  }

  // Fund Transfer
  transferFunds(fromAccountId: string, toAccountId: string, amount: number, description: string, referenceNo: string): Observable<Response> {
    return this.http.post<Response>(AccountingApiConstants.TRANSFER_FUNDS, null, {
      params: {
        fromAccountId: fromAccountId,
        toAccountId: toAccountId,
        amount: amount.toString(),
        description: description,
        referenceNo: referenceNo
      }
    });
  }

  processPurchasePayment(accountId: string, amount: number, purchaseInvoiceNo: string, supplierName: string): Observable<boolean> {
    return this.http.post<boolean>(AccountingApiConstants.PROCESS_PURCHASE_PAYMENT, null, {
      params: {
        accountId: accountId,
        amount: amount.toString(),
        purchaseInvoiceNo: purchaseInvoiceNo,
        supplierName: supplierName
      }
    });
  }

  // Utility Methods
  getLastTransaction(accountId: string): Observable<AccountTransaction> {
    return this.http.get<AccountTransaction>(AccountingApiConstants.GET_LAST_TRANSACTION, {
      params: { accountId: accountId }
    });
  }

  calculateBalanceFromTransactions(accountId: string): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.CALCULATE_BALANCE_FROM_TRANSACTIONS, {
      params: { accountId: accountId }
    });
  }

  validateSufficientBalance(accountId: string, amount: number): Observable<boolean> {
    return this.http.get<boolean>(AccountingApiConstants.VALIDATE_SUFFICIENT_BALANCE, {
      params: { accountId: accountId, amount: amount.toString() }
    });
  }
}
