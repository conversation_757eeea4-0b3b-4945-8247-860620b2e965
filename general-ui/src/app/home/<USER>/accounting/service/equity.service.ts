import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Equity } from '../model/equity';
import { AccountingApiConstants } from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class EquityService {

  constructor(private http: HttpClient) { }

  save(equity: Equity): Observable<any> {
    return this.http.post(AccountingApiConstants.SAVE_EQUITY, equity);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(AccountingApiConstants.FIND_ALL_EQUITY, {
      params: { page: page.toString(), pageSize: pageSize.toString() }
    });
  }

  findById(id: string): Observable<Equity> {
    return this.http.get<Equity>(AccountingApiConstants.FIND_EQUITY_BY_ID, {
      params: { id: id }
    });
  }

  findByType(equityType: string): Observable<Equity[]> {
    return this.http.get<Equity[]>(AccountingApiConstants.FIND_EQUITY_BY_TYPE, {
      params: { equityType: equityType }
    });
  }

  findByFiscalYear(fiscalYear: string): Observable<Equity[]> {
    return this.http.get<Equity[]>(AccountingApiConstants.FIND_EQUITY_BY_FISCAL_YEAR, {
      params: { fiscalYear: fiscalYear }
    });
  }

  findByOwner(ownerName: string): Observable<Equity[]> {
    return this.http.get<Equity[]>(AccountingApiConstants.FIND_EQUITY_BY_OWNER, {
      params: { ownerName: ownerName }
    });
  }

  getTotalEquityValue(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.GET_TOTAL_EQUITY_VALUE);
  }

  getEquityByTypeValue(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.GET_EQUITY_BY_TYPE_VALUE);
  }

  getRetainedEarnings(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.GET_RETAINED_EARNINGS);
  }

  getTotalCapital(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.GET_TOTAL_CAPITAL);
  }

  recordProfitRetention(amount: number, fiscalYear: string, notes: string): Observable<any> {
    return this.http.post(AccountingApiConstants.RECORD_PROFIT_RETENTION, {
      amount: amount,
      fiscalYear: fiscalYear,
      notes: notes
    });
  }

  recordOwnerDrawing(amount: number, ownerName: string, notes: string): Observable<any> {
    return this.http.post(AccountingApiConstants.RECORD_OWNER_DRAWING, {
      amount: amount,
      ownerName: ownerName,
      notes: notes
    });
  }
}
