import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AccountingApiConstants } from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class AccountsReceivableService {

  constructor(private http: HttpClient) { }

  getOutstandingInvoices(): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AR_OUTSTANDING_INVOICES);
  }

  getOutstandingInvoicesByCustomer(customerId: string): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AR_OUTSTANDING_BY_CUSTOMER, {
      params: { customerId: customerId }
    });
  }

  getCustomerOutstandingBalances(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.AR_CUSTOMER_BALANCES);
  }

  getAgingAnalysis(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.AR_AGING_ANALYSIS);
  }

  getTotalAccountsReceivable(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.AR_TOTAL);
  }

  getOverdueInvoices(): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AR_OVERDUE_INVOICES);
  }

  getTopCustomersByOutstanding(limit: number = 10): Observable<any[]> {
    return this.http.get<any[]>(AccountingApiConstants.AR_TOP_CUSTOMERS, {
      params: { limit: limit.toString() }
    });
  }
}
