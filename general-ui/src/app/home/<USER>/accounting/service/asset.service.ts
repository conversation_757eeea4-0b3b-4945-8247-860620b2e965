import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Asset } from '../model/asset';
import { AccountingApiConstants } from '../accounting-api-constants';

@Injectable({
  providedIn: 'root'
})
export class AssetService {

  constructor(private http: HttpClient) { }

  save(asset: Asset): Observable<any> {
    return this.http.post(AccountingApiConstants.SAVE_ASSET, asset);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(AccountingApiConstants.FIND_ALL_ASSETS, {
      params: { page: page.toString(), pageSize: pageSize.toString() }
    });
  }

  findById(id: string): Observable<Asset> {
    return this.http.get<Asset>(AccountingApiConstants.FIND_ASSET_BY_ID, {
      params: { id: id }
    });
  }

  findByType(assetType: string): Observable<Asset[]> {
    return this.http.get<Asset[]>(AccountingApiConstants.FIND_ASSETS_BY_TYPE, {
      params: { assetType: assetType }
    });
  }

  findByCategory(category: string): Observable<Asset[]> {
    return this.http.get<Asset[]>(AccountingApiConstants.FIND_ASSETS_BY_CATEGORY, {
      params: { category: category }
    });
  }

  findByStatus(status: string): Observable<Asset[]> {
    return this.http.get<Asset[]>(AccountingApiConstants.FIND_ASSETS_BY_STATUS, {
      params: { status: status }
    });
  }

  calculateDepreciation(id: string): Observable<any> {
    return this.http.post(AccountingApiConstants.CALCULATE_ASSET_DEPRECIATION, { id: id });
  }

  getTotalAssetValue(): Observable<number> {
    return this.http.get<number>(AccountingApiConstants.GET_TOTAL_ASSET_VALUE);
  }

  getAssetsByTypeValue(): Observable<any> {
    return this.http.get<any>(AccountingApiConstants.GET_ASSETS_BY_TYPE_VALUE);
  }

  disposeAsset(id: string, disposalDate: Date, disposalValue: number, reason: string): Observable<any> {
    return this.http.post(AccountingApiConstants.DISPOSE_ASSET, {
      id: id,
      disposalDate: disposalDate,
      disposalValue: disposalValue,
      reason: reason
    });
  }
}
