import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';

import {PettyCashManagementComponent} from './components/petty-cash-management/petty-cash-management.component';
import {AccountManagementComponent} from './components/account-management/account-management.component';
import {FinancialDashboardComponent} from '../dashboard/components/financial-dashboard/financial-dashboard.component';
import {AssetManagementComponent} from './components/asset-management/asset-management.component';
import {LiabilityManagementComponent} from './components/liability-management/liability-management.component';
import {EquityManagementComponent} from './components/equity-management/equity-management.component';

const routes: Routes = [
  {
    path: 'bank_account',
    component: AccountManagementComponent,
    data: {title: 'Bank Account Management'}
  },
  {
    path: 'petty_cash',
    component: PettyCashManagementComponent,
    data: {title: 'Petty Cash Management'}
  },
  {
    path: 'asset_management',
    component: AssetManagementComponent,
    data: {title: 'Asset Management'}
  },
  {
    path: 'liability_management',
    component: LiabilityManagementComponent,
    data: {title: 'Liability Management'}
  },
  {
    path: 'equity_management',
    component: EquityManagementComponent,
    data: {title: 'Equity Management'}
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AccountingRoutingModule {
}

export const accountRouteParams = [AccountManagementComponent, PettyCashManagementComponent, FinancialDashboardComponent,
  AssetManagementComponent, LiabilityManagementComponent, EquityManagementComponent];

