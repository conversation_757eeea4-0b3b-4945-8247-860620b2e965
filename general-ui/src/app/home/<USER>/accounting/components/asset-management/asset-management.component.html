<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Asset Management</h2>
    </div>
  </div>

  <div class="row">
    <!-- Create Asset Form -->
    <div class="col-lg-5">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Add/Edit Asset</h5>
        </div>
        <div class="card-body">
          <form #assetForm="ngForm">
            <!-- Asset Name -->
            <div class="mb-3">
              <label class="form-label">Asset Name *</label>
              <input type="text" 
                     class="form-control" 
                     name="assetName"
                     [(ngModel)]="asset.assetName"
                     required
                     placeholder="Enter asset name">
            </div>

            <!-- Asset Type -->
            <div class="mb-3">
              <label class="form-label">Asset Type *</label>
              <select class="form-select" 
                      name="assetType"
                      [(ngModel)]="asset.assetType"
                      required>
                <option value="CURRENT">Current Asset</option>
                <option value="FIXED">Fixed Asset</option>
              </select>
            </div>

            <!-- Category -->
            <div class="mb-3">
              <label class="form-label">Category</label>
              <select class="form-select" 
                      name="category"
                      [(ngModel)]="asset.category">
                <option value="">Select Category</option>
                <option *ngFor="let cat of categories" [value]="cat">{{ cat }}</option>
              </select>
            </div>

            <!-- Purchase Value -->
            <div class="mb-3">
              <label class="form-label">Purchase Value *</label>
              <input type="number" 
                     class="form-control" 
                     name="purchaseValue"
                     [(ngModel)]="asset.purchaseValue"
                     required
                     min="0"
                     step="0.01"
                     placeholder="Enter purchase value">
            </div>

            <!-- Purchase Date -->
            <div class="mb-3">
              <label class="form-label">Purchase Date</label>
              <input type="date" 
                     class="form-control" 
                     name="purchaseDate"
                     [(ngModel)]="asset.purchaseDate">
            </div>

            <!-- Description -->
            <div class="mb-3">
              <label class="form-label">Description</label>
              <textarea class="form-control" 
                        name="description"
                        [(ngModel)]="asset.description"
                        rows="3"
                        placeholder="Enter asset description"></textarea>
            </div>

            <!-- Fixed Asset Fields -->
            <div *ngIf="asset.assetType === 'FIXED'">
              <!-- Depreciation Method -->
              <div class="mb-3">
                <label class="form-label">Depreciation Method</label>
                <select class="form-select" 
                        name="depreciationMethod"
                        [(ngModel)]="asset.depreciationMethod">
                  <option *ngFor="let method of depreciationMethods" [value]="method.value">
                    {{ method.label }}
                  </option>
                </select>
              </div>

              <!-- Useful Life -->
              <div class="mb-3">
                <label class="form-label">Useful Life (Years)</label>
                <input type="number" 
                       class="form-control" 
                       name="usefulLife"
                       [(ngModel)]="asset.usefulLife"
                       min="1"
                       max="50"
                       placeholder="Enter useful life in years">
              </div>

              <!-- Depreciation Rate -->
              <div class="mb-3" *ngIf="asset.depreciationMethod === 'DECLINING_BALANCE'">
                <label class="form-label">Depreciation Rate (%)</label>
                <input type="number" 
                       class="form-control" 
                       name="depreciationRate"
                       [(ngModel)]="asset.depreciationRate"
                       min="0"
                       max="100"
                       step="0.1"
                       placeholder="Enter depreciation rate">
              </div>
            </div>

            <!-- Additional Fields -->
            <div class="mb-3">
              <label class="form-label">Location</label>
              <input type="text" 
                     class="form-control" 
                     name="location"
                     [(ngModel)]="asset.location"
                     placeholder="Enter asset location">
            </div>

            <div class="mb-3">
              <label class="form-label">Serial Number</label>
              <input type="text" 
                     class="form-control" 
                     name="serialNumber"
                     [(ngModel)]="asset.serialNumber"
                     placeholder="Enter serial number">
            </div>

            <!-- Buttons -->
            <div class="d-flex gap-2 justify-content-end">
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
              <button type="submit" 
                      class="btn btn-primary" 
                      [disabled]="!assetForm.valid"
                      (click)="save(assetForm)">
                <i class="fas fa-save me-1"></i>Save Asset
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Assets List -->
    <div class="col-lg-7">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Assets</h5>
            <div class="d-flex gap-2">
              <!-- Filters -->
              <select class="form-select form-select-sm" 
                      [(ngModel)]="typeFilter" 
                      (change)="onFilterChange()"
                      style="width: auto;">
                <option *ngFor="let type of assetTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
              <select class="form-select form-select-sm" 
                      [(ngModel)]="statusFilter" 
                      (change)="onFilterChange()"
                      style="width: auto;">
                <option *ngFor="let status of statusOptions" [value]="status.value">
                  {{ status.label }}
                </option>
              </select>
              <button class="btn btn-outline-secondary btn-sm" (click)="clearFilters()">
                <i class="fas fa-times"></i> Clear
              </button>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Asset Name</th>
                  <th>Type</th>
                  <th>Category</th>
                  <th>Purchase Value</th>
                  <th>Current Value</th>
                  <th>Status</th>
                  <th>Age</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let asset of assets; let i = index"
                    [class.table-active]="selectedAsset?.id === asset.id"
                    (click)="selectAsset(asset, i)"
                    style="cursor: pointer;">
                  <td>
                    <div>
                      <strong>{{ asset.assetName }}</strong>
                      <br>
                      <small class="text-muted">{{ asset.serialNumber || 'No S/N' }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge" [class]="getTypeClass(asset.assetType)">
                      {{ asset.assetType }}
                    </span>
                  </td>
                  <td>{{ asset.category || 'N/A' }}</td>
                  <td>{{ formatCurrency(asset.purchaseValue) }}</td>
                  <td>{{ formatCurrency(asset.currentValue) }}</td>
                  <td>
                    <span class="badge" [class]="getStatusClass(asset.status)">
                      {{ asset.status }}
                    </span>
                  </td>
                  <td>{{ calculateAge(asset.purchaseDate) }}</td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary btn-sm" 
                              (click)="editAsset(); $event.stopPropagation()"
                              title="Edit">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-outline-info btn-sm" 
                              *ngIf="asset.assetType === 'FIXED'"
                              (click)="calculateDepreciation(); $event.stopPropagation()"
                              title="Calculate Depreciation">
                        <i class="fas fa-calculator"></i>
                      </button>
                      <button class="btn btn-outline-danger btn-sm" 
                              *ngIf="asset.status === 'ACTIVE'"
                              (click)="disposeAsset(); $event.stopPropagation()"
                              title="Dispose">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="assets.length === 0">
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No assets found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer" *ngIf="collectionSize > pageSize">
          <ngb-pagination 
            [(page)]="page" 
            [pageSize]="pageSize" 
            [collectionSize]="collectionSize"
            (pageChange)="pageChanged($event)"
            [maxSize]="5"
            [rotate]="true"
            class="d-flex justify-content-center">
          </ngb-pagination>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Asset Details -->
  <div class="row mt-4" *ngIf="selectedAsset">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Asset Details</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Asset Name:</strong></td>
                  <td>{{ selectedAsset.assetName }}</td>
                </tr>
                <tr>
                  <td><strong>Type:</strong></td>
                  <td>
                    <span class="badge" [class]="getTypeClass(selectedAsset.assetType)">
                      {{ selectedAsset.assetType }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Category:</strong></td>
                  <td>{{ selectedAsset.category || 'N/A' }}</td>
                </tr>
                <tr>
                  <td><strong>Purchase Value:</strong></td>
                  <td>{{ formatCurrency(selectedAsset.purchaseValue) }}</td>
                </tr>
                <tr>
                  <td><strong>Current Value:</strong></td>
                  <td>{{ formatCurrency(selectedAsset.currentValue) }}</td>
                </tr>
                <tr>
                  <td><strong>Purchase Date:</strong></td>
                  <td>{{ formatDate(selectedAsset.purchaseDate) }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Status:</strong></td>
                  <td>
                    <span class="badge" [class]="getStatusClass(selectedAsset.status)">
                      {{ selectedAsset.status }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Location:</strong></td>
                  <td>{{ selectedAsset.location || 'N/A' }}</td>
                </tr>
                <tr>
                  <td><strong>Serial Number:</strong></td>
                  <td>{{ selectedAsset.serialNumber || 'N/A' }}</td>
                </tr>
                <tr *ngIf="selectedAsset.assetType === 'FIXED'">
                  <td><strong>Depreciation Method:</strong></td>
                  <td>{{ selectedAsset.depreciationMethod || 'N/A' }}</td>
                </tr>
                <tr *ngIf="selectedAsset.assetType === 'FIXED'">
                  <td><strong>Useful Life:</strong></td>
                  <td>{{ selectedAsset.usefulLife || 'N/A' }} years</td>
                </tr>
                <tr>
                  <td><strong>Age:</strong></td>
                  <td>{{ calculateAge(selectedAsset.purchaseDate) }}</td>
                </tr>
              </table>
            </div>
          </div>
          <div class="row" *ngIf="selectedAsset.description">
            <div class="col-12">
              <strong>Description:</strong>
              <p class="mt-2">{{ selectedAsset.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
