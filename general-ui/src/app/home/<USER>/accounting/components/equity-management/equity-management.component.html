<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Equity Management</h2>
    </div>
  </div>

  <!-- Summary Cards -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body text-center">
          <h6>Total Capital</h6>
          <h4>{{ formatCurrency(totalCapital) }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body text-center">
          <h6>Retained Earnings</h6>
          <h4>{{ formatCurrency(totalRetainedEarnings) }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-primary text-white">
        <div class="card-body text-center">
          <h6>Total Equity</h6>
          <h4>{{ formatCurrency(totalEquity) }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-secondary text-white">
        <div class="card-body text-center">
          <h6>Quick Actions</h6>
          <div class="btn-group-vertical w-100">
            <button class="btn btn-light btn-sm" (click)="recordProfitRetention()">
              <i class="fas fa-piggy-bank"></i> Profit Retention
            </button>
            <button class="btn btn-light btn-sm mt-1" (click)="recordOwnerDrawing()">
              <i class="fas fa-arrow-down"></i> Owner Drawing
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <!-- Create Equity Form -->
    <div class="col-lg-5">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Add/Edit Equity Transaction</h5>
        </div>
        <div class="card-body">
          <form #equityForm="ngForm">
            <!-- Equity Type -->
            <div class="mb-3">
              <label class="form-label">Equity Type *</label>
              <select class="form-select" 
                      name="equityType"
                      [(ngModel)]="equity.equityType"
                      required>
                <option value="CAPITAL">Capital</option>
                <option value="RETAINED_EARNINGS">Retained Earnings</option>
                <option value="DRAWINGS">Drawings</option>
              </select>
            </div>

            <!-- Transaction Type -->
            <div class="mb-3">
              <label class="form-label">Transaction Type *</label>
              <select class="form-select" 
                      name="transactionType"
                      [(ngModel)]="equity.transactionType"
                      required>
                <option *ngFor="let type of transactionTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
            </div>

            <!-- Owner Name -->
            <div class="mb-3" *ngIf="equity.equityType === 'CAPITAL' || equity.equityType === 'DRAWINGS'">
              <label class="form-label">Owner Name *</label>
              <input type="text" 
                     class="form-control" 
                     name="ownerName"
                     [(ngModel)]="equity.ownerName"
                     [required]="equity.equityType === 'CAPITAL' || equity.equityType === 'DRAWINGS'"
                     placeholder="Enter owner name">
            </div>

            <!-- Amount -->
            <div class="mb-3">
              <label class="form-label">Amount *</label>
              <input type="number" 
                     class="form-control" 
                     name="amount"
                     [(ngModel)]="equity.amount"
                     required
                     min="0"
                     step="0.01"
                     placeholder="Enter amount">
            </div>

            <!-- Transaction Date -->
            <div class="mb-3">
              <label class="form-label">Transaction Date</label>
              <input type="date" 
                     class="form-control" 
                     name="transactionDate"
                     [(ngModel)]="equity.transactionDate">
            </div>

            <!-- Fiscal Year -->
            <div class="mb-3">
              <label class="form-label">Fiscal Year</label>
              <select class="form-select" 
                      name="fiscalYear"
                      [(ngModel)]="equity.fiscalYear">
                <option *ngFor="let year of fiscalYears" [value]="year">{{ year }}</option>
              </select>
            </div>

            <!-- Quarter -->
            <div class="mb-3">
              <label class="form-label">Quarter</label>
              <select class="form-select" 
                      name="quarter"
                      [(ngModel)]="equity.quarter">
                <option value="">Select Quarter</option>
                <option *ngFor="let quarter of quarters" [value]="quarter.value">
                  {{ quarter.label }}
                </option>
              </select>
            </div>

            <!-- Reference Number -->
            <div class="mb-3">
              <label class="form-label">Reference Number</label>
              <input type="text" 
                     class="form-control" 
                     name="referenceNumber"
                     [(ngModel)]="equity.referenceNumber"
                     placeholder="Enter reference number">
            </div>

            <!-- Description -->
            <div class="mb-3">
              <label class="form-label">Description *</label>
              <textarea class="form-control" 
                        name="description"
                        [(ngModel)]="equity.description"
                        required
                        rows="3"
                        placeholder="Enter transaction description"></textarea>
            </div>

            <!-- Notes -->
            <div class="mb-3">
              <label class="form-label">Notes</label>
              <textarea class="form-control" 
                        name="notes"
                        [(ngModel)]="equity.notes"
                        rows="2"
                        placeholder="Enter additional notes"></textarea>
            </div>

            <!-- Buttons -->
            <div class="d-flex gap-2 justify-content-end">
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
              <button type="submit" 
                      class="btn btn-primary" 
                      [disabled]="!equityForm.valid"
                      (click)="save(equityForm)">
                <i class="fas fa-save me-1"></i>Save Transaction
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Equity Transactions List -->
    <div class="col-lg-7">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Equity Transactions</h5>
            <div class="d-flex gap-2">
              <!-- Filters -->
              <select class="form-select form-select-sm" 
                      [(ngModel)]="typeFilter" 
                      (change)="onFilterChange()"
                      style="width: auto;">
                <option *ngFor="let type of equityTypes" [value]="type.value">
                  {{ type.label }}
                </option>
              </select>
              <select class="form-select form-select-sm" 
                      [(ngModel)]="fiscalYearFilter" 
                      (change)="onFilterChange()"
                      style="width: auto;">
                <option value="ALL">All Years</option>
                <option *ngFor="let year of fiscalYears" [value]="year">{{ year }}</option>
              </select>
              <button class="btn btn-outline-secondary btn-sm" (click)="clearFilters()">
                <i class="fas fa-times"></i> Clear
              </button>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Transaction</th>
                  <th>Owner</th>
                  <th>Amount</th>
                  <th>Description</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let equity of equities; let i = index"
                    [class.table-active]="selectedEquity?.id === equity.id"
                    (click)="selectEquity(equity, i)"
                    style="cursor: pointer;">
                  <td>
                    <div>
                      {{ formatDate(equity.transactionDate) }}
                      <br>
                      <small class="text-muted">{{ equity.fiscalYear }} {{ equity.quarter }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge" [class]="getTypeClass(equity.equityType)">
                      {{ equity.equityType }}
                    </span>
                  </td>
                  <td>
                    <div [class]="getTransactionTypeClass(equity.transactionType)">
                      <i [class]="getTransactionIcon(equity.transactionType)"></i>
                      {{ equity.transactionType }}
                    </div>
                  </td>
                  <td>{{ equity.ownerName || 'N/A' }}</td>
                  <td>
                    <span [class]="equity.transactionType === 'WITHDRAWAL' ? 'text-danger' : 'text-success'">
                      {{ equity.transactionType === 'WITHDRAWAL' ? '-' : '+' }}{{ formatCurrency(equity.amount) }}
                    </span>
                  </td>
                  <td>
                    <span [title]="equity.description" class="text-truncate d-inline-block" style="max-width: 150px;">
                      {{ equity.description }}
                    </span>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary btn-sm" 
                              (click)="editEquity(); $event.stopPropagation()"
                              title="Edit">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="equities.length === 0">
                  <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No equity transactions found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        
        <!-- Pagination -->
        <div class="card-footer" *ngIf="collectionSize > pageSize">
          <ngb-pagination 
            [(page)]="page" 
            [pageSize]="pageSize" 
            [collectionSize]="collectionSize"
            (pageChange)="pageChanged($event)"
            [maxSize]="5"
            [rotate]="true"
            class="d-flex justify-content-center">
          </ngb-pagination>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Equity Details -->
  <div class="row mt-4" *ngIf="selectedEquity">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Transaction Details</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Equity Type:</strong></td>
                  <td>
                    <span class="badge" [class]="getTypeClass(selectedEquity.equityType)">
                      {{ selectedEquity.equityType }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Transaction Type:</strong></td>
                  <td>
                    <span [class]="getTransactionTypeClass(selectedEquity.transactionType)">
                      <i [class]="getTransactionIcon(selectedEquity.transactionType)"></i>
                      {{ selectedEquity.transactionType }}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td><strong>Owner:</strong></td>
                  <td>{{ selectedEquity.ownerName || 'N/A' }}</td>
                </tr>
                <tr>
                  <td><strong>Amount:</strong></td>
                  <td>
                    <span [class]="selectedEquity.transactionType === 'WITHDRAWAL' ? 'text-danger' : 'text-success'">
                      {{ selectedEquity.transactionType === 'WITHDRAWAL' ? '-' : '+' }}{{ formatCurrency(selectedEquity.amount) }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Transaction Date:</strong></td>
                  <td>{{ formatDate(selectedEquity.transactionDate) }}</td>
                </tr>
                <tr>
                  <td><strong>Fiscal Year:</strong></td>
                  <td>{{ selectedEquity.fiscalYear }}</td>
                </tr>
                <tr>
                  <td><strong>Quarter:</strong></td>
                  <td>{{ selectedEquity.quarter || 'N/A' }}</td>
                </tr>
                <tr>
                  <td><strong>Reference Number:</strong></td>
                  <td>{{ selectedEquity.referenceNumber || 'N/A' }}</td>
                </tr>
              </table>
            </div>
          </div>
          <div class="row">
            <div class="col-12">
              <strong>Description:</strong>
              <p class="mt-2">{{ selectedEquity.description }}</p>
              <div *ngIf="selectedEquity.notes">
                <strong>Notes:</strong>
                <p class="mt-2">{{ selectedEquity.notes }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
