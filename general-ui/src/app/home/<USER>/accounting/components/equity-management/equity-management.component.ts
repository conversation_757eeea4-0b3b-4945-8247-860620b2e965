import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Equity } from '../../model/equity';
import { EquityService } from '../../service/equity.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-equity-management',
  templateUrl: './equity-management.component.html',
  styleUrls: ['./equity-management.component.css']
})
export class EquityManagementComponent implements OnInit {

  equity = new Equity();
  equities: Array<Equity> = [];
  selectedEquity: Equity = null;

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Filters
  typeFilter = 'ALL';
  fiscalYearFilter = 'ALL';
  ownerFilter = 'ALL';

  // Summary
  totalCapital = 0;
  totalRetainedEarnings = 0;
  totalEquity = 0;

  // Options
  equityTypes = [
    { value: 'ALL', label: 'All Types' },
    { value: 'CAPITAL', label: 'Capital' },
    { value: 'RETAINED_EARNINGS', label: 'Retained Earnings' },
    { value: 'DRAWINGS', label: 'Drawings' }
  ];

  transactionTypes = [
    { value: 'INVESTMENT', label: 'Investment' },
    { value: 'WITHDRAWAL', label: 'Withdrawal' },
    { value: 'PROFIT_RETENTION', label: 'Profit Retention' }
  ];

  fiscalYears = [
    '2024', '2023', '2022', '2021', '2020'
  ];

  quarters = [
    { value: 'Q1', label: 'Q1 (Jan-Mar)' },
    { value: 'Q2', label: 'Q2 (Apr-Jun)' },
    { value: 'Q3', label: 'Q3 (Jul-Sep)' },
    { value: 'Q4', label: 'Q4 (Oct-Dec)' }
  ];

  constructor(
    private equityService: EquityService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.loadEquities();
    this.loadSummary();
  }

  initializeForm(): void {
    this.equity = new Equity();
    this.equity.transactionDate = new Date();
    this.equity.fiscalYear = new Date().getFullYear().toString();
  }

  save(form: NgForm): void {
    if (!this.equity.description || this.equity.description.trim() === '') {
      this.notificationService.showError('Please enter description');
      return;
    }

    if (!this.equity.amount || this.equity.amount <= 0) {
      this.notificationService.showError('Please enter a valid amount');
      return;
    }

    if (this.equity.equityType === 'CAPITAL' && !this.equity.ownerName) {
      this.notificationService.showError('Please enter owner name for capital transactions');
      return;
    }

    this.equityService.save(this.equity).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Equity transaction saved successfully', 'Failed to save equity transaction');
        form.reset();
        this.initializeForm();
        this.loadEquities();
        this.loadSummary();
      },
      (error) => {
        this.notificationService.showError('Failed to save equity transaction: ' + (error.message || 'Unknown error'));
      }
    );
  }

  loadEquities(): void {
    this.equityService.findAll(this.page - 1, this.pageSize).subscribe(
      (data: any) => {
        this.equities = data.content || data;
        this.collectionSize = data.totalElements || this.equities.length;
        this.applyFilters();
      },
      (error) => {
        console.error('Error loading equities:', error);
        this.equities = [];
      }
    );
  }

  loadSummary(): void {
    this.equityService.getTotalCapital().subscribe(
      (data: number) => {
        this.totalCapital = data || 0;
      }
    );

    this.equityService.getRetainedEarnings().subscribe(
      (data: number) => {
        this.totalRetainedEarnings = data || 0;
      }
    );

    this.equityService.getTotalEquityValue().subscribe(
      (data: number) => {
        this.totalEquity = data || 0;
      }
    );
  }

  selectEquity(equity: Equity, index: number): void {
    this.selectedEquity = equity;
  }

  editEquity(): void {
    if (!this.selectedEquity) {
      this.notificationService.showError('Please select an equity transaction to edit');
      return;
    }
    this.equity = { ...this.selectedEquity };
  }

  recordProfitRetention(): void {
    const amount = prompt('Enter profit retention amount:');
    const notes = prompt('Enter notes:');

    if (amount && parseFloat(amount) > 0) {
      this.equityService.recordProfitRetention(
        parseFloat(amount),
        new Date().getFullYear().toString(),
        notes || ''
      ).subscribe(
        (result) => {
          this.notificationService.handleResponse(result, 'Profit retention recorded successfully', 'Failed to record profit retention');
          this.loadEquities();
          this.loadSummary();
        },
        (error) => {
          this.notificationService.showError('Failed to record profit retention: ' + (error.message || 'Unknown error'));
        }
      );
    }
  }

  recordOwnerDrawing(): void {
    const ownerName = prompt('Enter owner name:');
    const amount = prompt('Enter drawing amount:');
    const notes = prompt('Enter notes:');

    if (ownerName && amount && parseFloat(amount) > 0) {
      this.equityService.recordOwnerDrawing(
        parseFloat(amount),
        ownerName,
        notes || ''
      ).subscribe(
        (result) => {
          this.notificationService.handleResponse(result, 'Owner drawing recorded successfully', 'Failed to record owner drawing');
          this.loadEquities();
          this.loadSummary();
        },
        (error) => {
          this.notificationService.showError('Failed to record owner drawing: ' + (error.message || 'Unknown error'));
        }
      );
    }
  }

  applyFilters(): void {
    let filteredEquities = [...this.equities];

    if (this.typeFilter !== 'ALL') {
      filteredEquities = filteredEquities.filter(equity => equity.equityType === this.typeFilter);
    }

    if (this.fiscalYearFilter !== 'ALL') {
      filteredEquities = filteredEquities.filter(equity => equity.fiscalYear === this.fiscalYearFilter);
    }

    if (this.ownerFilter !== 'ALL') {
      filteredEquities = filteredEquities.filter(equity => equity.ownerName === this.ownerFilter);
    }

    this.equities = filteredEquities;
    this.collectionSize = this.equities.length;
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  clearFilters(): void {
    this.typeFilter = 'ALL';
    this.fiscalYearFilter = 'ALL';
    this.ownerFilter = 'ALL';
    this.loadEquities();
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadEquities();
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }

  formatDate(date: any): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }

  getTypeClass(type: string): string {
    switch (type) {
      case 'CAPITAL': return 'badge-success';
      case 'RETAINED_EARNINGS': return 'badge-info';
      case 'DRAWINGS': return 'badge-warning';
      default: return 'badge-secondary';
    }
  }

  getTransactionTypeClass(type: string): string {
    switch (type) {
      case 'INVESTMENT': return 'text-success';
      case 'WITHDRAWAL': return 'text-danger';
      case 'PROFIT_RETENTION': return 'text-info';
      default: return 'text-muted';
    }
  }

  getTransactionIcon(type: string): string {
    switch (type) {
      case 'INVESTMENT': return 'fas fa-arrow-up';
      case 'WITHDRAWAL': return 'fas fa-arrow-down';
      case 'PROFIT_RETENTION': return 'fas fa-piggy-bank';
      default: return 'fas fa-exchange-alt';
    }
  }

  clear(): void {
    this.initializeForm();
  }
}
