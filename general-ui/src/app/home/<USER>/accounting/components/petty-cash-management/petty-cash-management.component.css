.table-active {
  background-color: #e3f2fd !important;
}

.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.badge {
  font-size: 0.75rem;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.modal-header {
  border-bottom: 1px solid #e0e0e0;
}

.modal-footer {
  border-top: 1px solid #e0e0e0;
}

.form-group label {
  font-weight: 500;
  color: #495057;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-top: none;
}

.table-responsive {
  border: 1px solid #e0e0e0;
  border-radius: 0.25rem;
}

.nav-tabs .nav-link {
  color: #495057;
}

.nav-tabs .nav-link.active {
  color: #007bff;
  border-color: #007bff #007bff #fff;
}
