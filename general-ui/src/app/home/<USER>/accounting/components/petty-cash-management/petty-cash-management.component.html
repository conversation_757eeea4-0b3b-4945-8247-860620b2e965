<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Petty Cash Management</h2>
    </div>
  </div>

  <div class="row">
    <!-- Petty Cash List -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">Petty Cash Funds</h5>
          <button class="btn btn-primary btn-sm" (click)="openPettyCashModal()">
            <i class="fa fa-plus"></i> Add New Fund
          </button>
        </div>
        <div class="card-body">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover">
              <thead class="thead-light">
                <tr>
                  <th>Fund No</th>
                  <th>Location</th>
                  <th>Custodian</th>
                  <th>Balance</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let fund of pettyCashList"
                    [class.table-active]="selectedPettyCash?.id === fund.id"
                    (click)="selectPettyCash(fund)"
                    style="cursor: pointer;">
                  <td>{{ fund.pettyCashNo }}</td>
                  <td>{{ fund.location }}</td>
                  <td>{{ fund.custodian }}</td>
                  <td class="text-right">{{ formatCurrency(fund.currentBalance) }}</td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary"
                              (click)="openPettyCashModal(fund); $event.stopPropagation()">
                        <i class="fa fa-edit"></i>
                      </button>
                      <button class="btn btn-outline-success"
                              (click)="openTransactionModal(fund); $event.stopPropagation()">
                        <i class="fa fa-money"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="pettyCashList.length === 0">
                  <td colspan="5" class="text-center text-muted">No petty cash funds found</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction History -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            Transaction History
            <span *ngIf="selectedPettyCash" class="text-muted">
              - {{ selectedPettyCash.location }}
            </span>
          </h5>
        </div>
        <div class="card-body">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-sm">
              <thead class="thead-light">
                <tr>
                  <th>Date</th>
                  <th>Type</th>
                  <th>Description</th>
                  <th>Amount</th>
                  <th>Balance</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let transaction of transactionList">
                  <td>{{ formatDate(transaction.createdDate) }}</td>
                  <td>
                    <span class="badge"
                          [class.badge-success]="transaction.operator === '+'"
                          [class.badge-danger]="transaction.operator === '-'">
                      {{ transaction.transactionType?.value }}
                    </span>
                  </td>
                  <td>{{ transaction.description }}</td>
                  <td class="text-right"
                      [class.text-success]="transaction.operator === '+'"
                      [class.text-danger]="transaction.operator === '-'">
                    {{ transaction.operator }}{{ formatCurrency(transaction.amount) }}
                  </td>
                  <td class="text-right">{{ formatCurrency(transaction.balanceAfter) }}</td>
                </tr>
                <tr *ngIf="!selectedPettyCash">
                  <td colspan="5" class="text-center text-muted">Select a petty cash fund to view transactions</td>
                </tr>
                <tr *ngIf="selectedPettyCash && transactionList.length === 0">
                  <td colspan="5" class="text-center text-muted">No transactions found</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Petty Cash Modal -->
<ng-template #pettyCashModal>
  <div class="modal-header">
    <h4 class="modal-title">{{ isEditMode ? 'Edit' : 'Add New' }} Petty Cash Fund</h4>
    <button type="button" class="btn-close" (click)="closeModal()"></button>
  </div>
  <div class="modal-body">
    <form>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="location">Location *</label>
            <input type="text"
                   class="form-control"
                   id="location"
                   [(ngModel)]="pettyCash.location"
                   name="location"
                   placeholder="Enter location">
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="custodian">Custodian *</label>
            <input type="text"
                   class="form-control"
                   id="custodian"
                   [(ngModel)]="pettyCash.custodian"
                   name="custodian"
                   placeholder="Enter custodian name">
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="openingBalance">Opening Balance</label>
            <input type="number"
                   class="form-control"
                   id="openingBalance"
                   [(ngModel)]="pettyCash.openingBalance"
                   name="openingBalance"
                   min="0"
                   step="0.01"
                   placeholder="0.00">
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-group mb-3">
            <label for="active">Status</label>
            <select class="form-control"
                    id="active"
                    [(ngModel)]="pettyCash.active"
                    name="active">
              <option [value]="true">Active</option>
              <option [value]="false">Inactive</option>
            </select>
          </div>
        </div>
      </div>
      <div class="form-group mb-3">
        <label for="remark">Remarks</label>
        <textarea class="form-control"
                  id="remark"
                  [(ngModel)]="pettyCash.remark"
                  name="remark"
                  rows="3"
                  placeholder="Enter any remarks"></textarea>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">Cancel</button>
    <button type="button"
            class="btn btn-primary"
            (click)="savePettyCash()"
            [disabled]="loading">
      <span *ngIf="loading" class="spinner-border spinner-border-sm me-2"></span>
      {{ isEditMode ? 'Update' : 'Save' }}
    </button>
  </div>
</ng-template>

<!-- Transaction Modal -->
<ng-template #transactionModal>
  <div class="modal-header">
    <h4 class="modal-title">
      Petty Cash Transaction - {{ selectedPettyCash?.location }}
      <small class="text-muted">(Balance: {{ formatCurrency(selectedPettyCash?.currentBalance) }})</small>
    </h4>
    <button type="button" class="btn-close" (click)="closeModal()"></button>
  </div>
  <div class="modal-body">
    <div class="row">
      <div class="col-12">
        <tabset>
          <!-- Replenishment Tab -->
          <tab heading="Replenish Fund">
            <div class="mt-3">
              <form>
                <div class="form-group mb-3">
                  <label for="replenishAmount">Amount *</label>
                  <input type="number"
                         class="form-control"
                         id="replenishAmount"
                         [(ngModel)]="transactionAmount"
                         name="replenishAmount"
                         min="0.01"
                         step="0.01"
                         placeholder="0.00">
                </div>
                <div class="form-group mb-3">
                  <label for="replenishDescription">Description *</label>
                  <input type="text"
                         class="form-control"
                         id="replenishDescription"
                         [(ngModel)]="transactionDescription"
                         name="replenishDescription"
                         placeholder="Enter description">
                </div>
                <div class="form-group mb-3">
                  <label for="replenishVoucher">Voucher No</label>
                  <input type="text"
                         class="form-control"
                         id="replenishVoucher"
                         [(ngModel)]="voucherNo"
                         name="replenishVoucher"
                         placeholder="Enter voucher number">
                </div>
                <button type="button"
                        class="btn btn-success"
                        (click)="replenishPettyCash()"
                        [disabled]="loading">
                  <span *ngIf="loading" class="spinner-border spinner-border-sm me-2"></span>
                  Replenish Fund
                </button>
              </form>
            </div>
          </tab>

          <!-- Expense Tab -->
          <tab heading="Record Expense">
            <div class="mt-3">
              <form>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group mb-3">
                      <label for="expenseAmount">Amount *</label>
                      <input type="number"
                             class="form-control"
                             id="expenseAmount"
                             [(ngModel)]="transactionAmount"
                             name="expenseAmount"
                             min="0.01"
                             step="0.01"
                             placeholder="0.00">
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group mb-3">
                      <label for="expenseCategory">Category *</label>
                      <select class="form-control"
                              id="expenseCategory"
                              [(ngModel)]="selectedExpenseCategory"
                              name="expenseCategory">
                        <option value="">Select category</option>
                        <option *ngFor="let category of expenseCategories"
                                [ngValue]="category">
                          {{ category.value }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="form-group mb-3">
                  <label for="expenseDescription">Description *</label>
                  <input type="text"
                         class="form-control"
                         id="expenseDescription"
                         [(ngModel)]="transactionDescription"
                         name="expenseDescription"
                         placeholder="Enter description">
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group mb-3">
                      <label for="expenseVoucher">Voucher No</label>
                      <input type="text"
                             class="form-control"
                             id="expenseVoucher"
                             [(ngModel)]="voucherNo"
                             name="expenseVoucher"
                             placeholder="Enter voucher number">
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group mb-3">
                      <label for="receivedBy">Received By *</label>
                      <input type="text"
                             class="form-control"
                             id="receivedBy"
                             [(ngModel)]="receivedBy"
                             name="receivedBy"
                             placeholder="Who received the money">
                    </div>
                  </div>
                </div>
                <button type="button"
                        class="btn btn-danger"
                        (click)="recordExpense()"
                        [disabled]="loading">
                  <span *ngIf="loading" class="spinner-border spinner-border-sm me-2"></span>
                  Record Expense
                </button>
              </form>
            </div>
          </tab>
        </tabset>
      </div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="closeModal()">Close</button>
  </div>
</ng-template>
