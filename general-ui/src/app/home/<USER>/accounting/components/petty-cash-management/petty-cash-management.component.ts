import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { PettyCash } from '../../model/petty-cash';
import { PettyCashTransaction } from '../../model/petty-cash-transaction';
import { PettyCashService } from '../../service/petty-cash.service';
import { MetaData } from "../../../../core/model/metaData";
import { MetaDataService } from "../../../../core/service/metaData.service";

@Component({
  standalone: false,
  selector: 'app-petty-cash-management',
  templateUrl: './petty-cash-management.component.html',
  styleUrls: ['./petty-cash-management.component.css']
})
export class PettyCashManagementComponent implements OnInit {

  @ViewChild('pettyCashModal', { static: false }) pettyCashModal: TemplateRef<any>;
  @ViewChild('transactionModal', { static: false }) transactionModal: TemplateRef<any>;

  // Data arrays
  pettyCashList: PettyCash[] = [];
  transactionList: PettyCashTransaction[] = [];
  expenseCategories: MetaData[] = [];
  transactionTypes: MetaData[] = [];

  // Form objects
  pettyCash: PettyCash = new PettyCash();
  transaction: PettyCashTransaction = new PettyCashTransaction();

  // Modal references
  modalRef: BsModalRef;

  // UI state
  isEditMode = false;
  selectedPettyCash: PettyCash = null;
  loading = false;

  // Transaction form fields
  transactionAmount: number = 0;
  transactionDescription: string = '';
  voucherNo: string = '';
  receivedBy: string = '';
  selectedExpenseCategory: MetaData = null;

  constructor(
    private pettyCashService: PettyCashService,
    private metaDataService: MetaDataService,
    private modalService: BsModalService
  ) { }

  ngOnInit(): void {
    this.loadPettyCashList();
    this.loadExpenseCategories();
    this.loadTransactionTypes();
  }

  // Load data methods
  loadPettyCashList(): void {
    this.loading = true;
    this.pettyCashService.findAllActive().subscribe(
      (data: PettyCash[]) => {
        this.pettyCashList = data || [];
        this.loading = false;
      },
      (error) => {
        console.error('Error loading petty cash list:', error);
        this.loading = false;
      }
    );
  }

  loadExpenseCategories(): void {
    this.metaDataService.findByCategory('ExpenseType').subscribe(
      (data: MetaData[]) => {
        this.expenseCategories = data || [];
      },
      (error) => {
        console.error('Error loading expense categories:', error);
      }
    );
  }

  loadTransactionTypes(): void {
    this.metaDataService.findByCategory('PettyCashTransactionType').subscribe(
      (data: MetaData[]) => {
        this.transactionTypes = data || [];
      },
      (error) => {
        console.error('Error loading transaction types:', error);
      }
    );
  }

  loadTransactions(pettyCashId: string): void {
    this.pettyCashService.findTransactionsByPettyCash(pettyCashId).subscribe(
      (data: PettyCashTransaction[]) => {
        this.transactionList = data || [];
      },
      (error) => {
        console.error('Error loading transactions:', error);
      }
    );
  }

  // Modal methods
  openPettyCashModal(pettyCash?: PettyCash): void {
    this.isEditMode = !!pettyCash;
    this.pettyCash = pettyCash ? { ...pettyCash } : new PettyCash();
    this.modalRef = this.modalService.show(this.pettyCashModal, { class: 'modal-lg' });
  }

  openTransactionModal(pettyCash: PettyCash): void {
    this.selectedPettyCash = pettyCash;
    this.resetTransactionForm();
    this.modalRef = this.modalService.show(this.transactionModal, { class: 'modal-lg' });
  }

  closeModal(): void {
    this.modalRef?.hide();
    this.resetForms();
  }

  // Form methods
  resetForms(): void {
    this.pettyCash = new PettyCash();
    this.resetTransactionForm();
    this.isEditMode = false;
    this.selectedPettyCash = null;
  }

  resetTransactionForm(): void {
    this.transactionAmount = 0;
    this.transactionDescription = '';
    this.voucherNo = '';
    this.receivedBy = '';
    this.selectedExpenseCategory = null;
  }

  // Save methods
  savePettyCash(): void {
    if (!this.validatePettyCashForm()) {
      return;
    }

    this.loading = true;
    this.pettyCashService.save(this.pettyCash).subscribe(
      (response) => {
        if (response.code === 200) {
          alert('Petty cash saved successfully');
          this.loadPettyCashList();
          this.closeModal();
        } else {
          alert('Error: ' + response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error saving petty cash:', error);
        alert('Error saving petty cash');
        this.loading = false;
      }
    );
  }

  // Transaction methods
  replenishPettyCash(): void {
    if (!this.validateTransactionForm()) {
      return;
    }

    this.loading = true;
    this.pettyCashService.replenishPettyCash(
      this.selectedPettyCash.id,
      this.transactionAmount,
      this.transactionDescription,
      this.voucherNo
    ).subscribe(
      (response) => {
        if (response.code === 200) {
          alert('Petty cash replenished successfully');
          this.loadPettyCashList();
          this.loadTransactions(this.selectedPettyCash.id);
          this.resetTransactionForm();
        } else {
          alert('Error: ' + response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error replenishing petty cash:', error);
        alert('Error replenishing petty cash');
        this.loading = false;
      }
    );
  }

  recordExpense(): void {
    if (!this.validateExpenseForm()) {
      return;
    }

    this.loading = true;
    this.pettyCashService.recordExpense(
      this.selectedPettyCash.id,
      this.transactionAmount,
      this.transactionDescription,
      this.selectedExpenseCategory.id,
      this.voucherNo,
      this.receivedBy
    ).subscribe(
      (response) => {
        if (response.code === 200) {
          alert('Expense recorded successfully');
          this.loadPettyCashList();
          this.loadTransactions(this.selectedPettyCash.id);
          this.resetTransactionForm();
        } else {
          alert('Error: ' + response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error recording expense:', error);
        alert('Error recording expense');
        this.loading = false;
      }
    );
  }

  // Validation methods
  validatePettyCashForm(): boolean {
    if (!this.pettyCash.location || this.pettyCash.location.trim() === '') {
      alert('Please enter location');
      return false;
    }
    if (!this.pettyCash.custodian || this.pettyCash.custodian.trim() === '') {
      alert('Please enter custodian name');
      return false;
    }
    if (this.pettyCash.openingBalance < 0) {
      alert('Opening balance cannot be negative');
      return false;
    }
    return true;
  }

  validateTransactionForm(): boolean {
    if (this.transactionAmount <= 0) {
      alert('Please enter a valid amount');
      return false;
    }
    if (!this.transactionDescription || this.transactionDescription.trim() === '') {
      alert('Please enter description');
      return false;
    }
    return true;
  }

  validateExpenseForm(): boolean {
    if (!this.validateTransactionForm()) {
      return false;
    }
    if (!this.selectedExpenseCategory) {
      alert('Please select expense category');
      return false;
    }
    if (!this.receivedBy || this.receivedBy.trim() === '') {
      alert('Please enter who received the money');
      return false;
    }
    return true;
  }

  // Utility methods
  selectPettyCash(pettyCash: PettyCash): void {
    this.selectedPettyCash = pettyCash;
    this.loadTransactions(pettyCash.id);
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toFixed(2) : '0.00';
  }

  formatDate(date: Date): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }
}
