import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Liability } from '../../model/liability';
import { LiabilityService } from '../../service/liability.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-liability-management',
  templateUrl: './liability-management.component.html',
  styleUrls: ['./liability-management.component.css']
})
export class LiabilityManagementComponent implements OnInit {

  liability = new Liability();
  liabilities: Array<Liability> = [];
  selectedLiability: Liability = null;

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Filters
  typeFilter = 'ALL';
  statusFilter = 'ALL';
  categoryFilter = 'ALL';

  // Payment
  paymentAmount: number = 0;
  paymentNotes: string = '';

  // Options
  liabilityTypes = [
    { value: 'ALL', label: 'All Types' },
    { value: 'CURRENT', label: 'Current Liabilities' },
    { value: 'LONG_TERM', label: 'Long-term Liabilities' }
  ];

  statusOptions = [
    { value: 'ALL', label: 'All Status' },
    { value: 'ACTIVE', label: 'Active' },
    { value: 'PAID', label: 'Paid' },
    { value: 'OVERDUE', label: 'Overdue' },
    { value: 'DEFAULTED', label: 'Defaulted' }
  ];

  categories = [
    'Accounts Payable', 'Bank Loan', 'Credit Card', 'Mortgage', 'Equipment Loan', 'Other'
  ];

  paymentFrequencies = [
    { value: 'MONTHLY', label: 'Monthly' },
    { value: 'QUARTERLY', label: 'Quarterly' },
    { value: 'ANNUALLY', label: 'Annually' }
  ];

  priorities = [
    { value: 'HIGH', label: 'High' },
    { value: 'MEDIUM', label: 'Medium' },
    { value: 'LOW', label: 'Low' }
  ];

  constructor(
    private liabilityService: LiabilityService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.loadLiabilities();
  }

  initializeForm(): void {
    this.liability = new Liability();
    this.liability.startDate = new Date();
    this.liability.dueDate = new Date();
  }

  save(form: NgForm): void {
    if (!this.liability.liabilityName || this.liability.liabilityName.trim() === '') {
      this.notificationService.showError('Please enter liability name');
      return;
    }

    if (!this.liability.originalAmount || this.liability.originalAmount <= 0) {
      this.notificationService.showError('Please enter a valid original amount');
      return;
    }

    if (!this.liability.creditorName || this.liability.creditorName.trim() === '') {
      this.notificationService.showError('Please enter creditor name');
      return;
    }

    this.liabilityService.save(this.liability).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Liability saved successfully', 'Failed to save liability');
        form.reset();
        this.initializeForm();
        this.loadLiabilities();
      },
      (error) => {
        this.notificationService.showError('Failed to save liability: ' + (error.message || 'Unknown error'));
      }
    );
  }

  loadLiabilities(): void {
    this.liabilityService.findAll(this.page - 1, this.pageSize).subscribe(
      (data: any) => {
        this.liabilities = data.content || data;
        this.collectionSize = data.totalElements || this.liabilities.length;
        this.applyFilters();
      },
      (error) => {
        console.error('Error loading liabilities:', error);
        this.liabilities = [];
      }
    );
  }

  selectLiability(liability: Liability, index: number): void {
    this.selectedLiability = liability;
    this.paymentAmount = 0;
    this.paymentNotes = '';
  }

  editLiability(): void {
    if (!this.selectedLiability) {
      this.notificationService.showError('Please select a liability to edit');
      return;
    }
    this.liability = { ...this.selectedLiability };
  }

  makePayment(): void {
    if (!this.selectedLiability) {
      this.notificationService.showError('Please select a liability to make payment');
      return;
    }

    if (!this.paymentAmount || this.paymentAmount <= 0) {
      this.notificationService.showError('Please enter a valid payment amount');
      return;
    }

    if (this.paymentAmount > this.selectedLiability.currentBalance) {
      this.notificationService.showError('Payment amount cannot exceed current balance');
      return;
    }

    this.liabilityService.makePayment(
      this.selectedLiability.id,
      this.paymentAmount,
      new Date(),
      this.paymentNotes
    ).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Payment recorded successfully', 'Failed to record payment');
        this.loadLiabilities();
        this.selectedLiability = null;
        this.paymentAmount = 0;
        this.paymentNotes = '';
      },
      (error) => {
        this.notificationService.showError('Failed to record payment: ' + (error.message || 'Unknown error'));
      }
    );
  }

  calculateInterest(): void {
    if (!this.selectedLiability) {
      this.notificationService.showError('Please select a liability to calculate interest');
      return;
    }

    this.liabilityService.calculateInterest(this.selectedLiability.id).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Interest calculated successfully', 'Failed to calculate interest');
        this.loadLiabilities();
      },
      (error) => {
        this.notificationService.showError('Failed to calculate interest: ' + (error.message || 'Unknown error'));
      }
    );
  }

  applyFilters(): void {
    let filteredLiabilities = [...this.liabilities];

    if (this.typeFilter !== 'ALL') {
      filteredLiabilities = filteredLiabilities.filter(liability => liability.liabilityType === this.typeFilter);
    }

    if (this.statusFilter !== 'ALL') {
      filteredLiabilities = filteredLiabilities.filter(liability => liability.status === this.statusFilter);
    }

    if (this.categoryFilter !== 'ALL') {
      filteredLiabilities = filteredLiabilities.filter(liability => liability.category === this.categoryFilter);
    }

    this.liabilities = filteredLiabilities;
    this.collectionSize = this.liabilities.length;
  }

  onFilterChange(): void {
    this.applyFilters();
  }

  clearFilters(): void {
    this.typeFilter = 'ALL';
    this.statusFilter = 'ALL';
    this.categoryFilter = 'ALL';
    this.loadLiabilities();
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadLiabilities();
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }

  formatDate(date: any): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'ACTIVE': return 'badge-success';
      case 'PAID': return 'badge-primary';
      case 'OVERDUE': return 'badge-danger';
      case 'DEFAULTED': return 'badge-dark';
      default: return 'badge-secondary';
    }
  }

  getTypeClass(type: string): string {
    switch (type) {
      case 'CURRENT': return 'badge-warning';
      case 'LONG_TERM': return 'badge-info';
      default: return 'badge-secondary';
    }
  }

  getPriorityClass(priority: string): string {
    switch (priority) {
      case 'HIGH': return 'text-danger';
      case 'MEDIUM': return 'text-warning';
      case 'LOW': return 'text-success';
      default: return 'text-muted';
    }
  }

  calculateDaysOverdue(dueDate: Date): number {
    if (!dueDate) return 0;
    const now = new Date();
    const due = new Date(dueDate);
    const diffTime = now.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  }

  clear(): void {
    this.initializeForm();
  }
}
