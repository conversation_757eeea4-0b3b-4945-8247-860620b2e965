.table-responsive {
  border-radius: 0.375rem;
}

.btn-group .btn {
  margin-right: 2px;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.table th {
  border-top: none;
  font-weight: 600;
  font-size: 0.875rem;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.table-secondary {
  opacity: 0.6;
}

.text-success {
  color: #28a745 !important;
}

.text-danger {
  color: #dc3545 !important;
}

.alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.form-group label {
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
}

.fa-2x {
  font-size: 2em;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mr-2 {
  margin-right: 0.5rem !important;
}
