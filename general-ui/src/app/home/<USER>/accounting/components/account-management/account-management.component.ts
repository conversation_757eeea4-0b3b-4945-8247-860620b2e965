import { Component, OnInit, TemplateRef, ViewChild } from '@angular/core';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { AccountService } from '../../service/account.service';
import { PaymentConstants } from '../../../trade/constants/payment-constants';
import { NotificationService } from '../../../../core/service/notification.service';
import { Response } from '../../../../core/model/response';
import {Account} from "../../model/account";
import {AccountTransaction} from "../../model/account-transaction";

@Component({
  standalone: false,
  selector: 'app-account-management',
  templateUrl: './account-management.component.html',
  styleUrls: ['./account-management.component.css']
})
export class AccountManagementComponent implements OnInit {

  @ViewChild('accountModal', { static: false }) accountModal: TemplateRef<any>;
  @ViewChild('transactionModal', { static: false }) transactionModal: TemplateRef<any>;
  @ViewChild('balanceCorrectionModal', { static: false }) balanceCorrectionModal: TemplateRef<any>;

  // Data arrays
  accountList: Account[] = [];
  transactionList: AccountTransaction[] = [];
  accountTypes: Array<{value: string, label: string}> = [];

  // Form objects
  account: Account = new Account();
  transaction: AccountTransaction = new AccountTransaction();

  // Modal references
  modalRef: BsModalRef;

  // UI state
  isEditMode = false;
  selectedAccount: Account = null;
  loading = false;

  // Transaction form fields
  transactionAmount: number = 0;
  transactionDescription: string = '';
  referenceNo: string = '';
  thirdParty: string = '';
  selectedTransactionType: string = PaymentConstants.TRANSACTION_TYPE_CREDIT;

  // Balance correction fields
  correctionAmount: number = 0;
  correctionReason: string = '';
  selectedAccountForCorrection: Account = null;

  // Search and filter
  searchTerm: string = '';
  selectedAccountType: string = '';

  constructor(
    private accountService: AccountService,
    private modalService: BsModalService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadAccountList();
    this.loadAccountTypes();
    this.resetForm();
  }

  // Load data methods
  loadAccountList(): void {
    this.loading = true;
    this.accountService.findAllAccounts().subscribe(
      (data: Account[]) => {
        this.accountList = data || [];
        this.loading = false;
      },
      (error) => {
        console.error('Error loading account list:', error);
        this.notificationService.showError('Error loading accounts');
        this.loading = false;
      }
    );
  }

  loadAccountTypes(): void {
    this.accountTypes = PaymentConstants.getAccountTypes();
  }

  loadTransactions(accountId: string): void {
    this.accountService.findTransactionsByAccount(accountId).subscribe(
      (data: AccountTransaction[]) => {
        this.transactionList = data || [];
      },
      (error) => {
        console.error('Error loading transactions:', error);
        this.notificationService.showError('Error loading transactions');
      }
    );
  }

  // Modal methods
  openAccountModal(): void {
    this.resetForm();
    this.isEditMode = false;
    this.modalRef = this.modalService.show(this.accountModal, { class: 'modal-lg' });
  }

  openEditModal(account: Account): void {
    this.account = { ...account };
    this.isEditMode = true;
    this.modalRef = this.modalService.show(this.accountModal, { class: 'modal-lg' });
  }

  openTransactionModal(account: Account): void {
    this.selectedAccount = account;
    this.loadTransactions(account.id);
    this.resetTransactionForm();
    this.modalRef = this.modalService.show(this.transactionModal, { class: 'modal-xl' });
  }

  openBalanceCorrectionModal(account: Account): void {
    this.selectedAccountForCorrection = account;
    this.correctionAmount = account.currentBalance;
    this.correctionReason = '';
    this.modalRef = this.modalService.show(this.balanceCorrectionModal, { class: 'modal-lg' });
  }

  closeModal(): void {
    if (this.modalRef) {
      this.modalRef.hide();
    }
    this.resetForm();
    this.resetTransactionForm();
    this.resetBalanceCorrectionForm();
  }

  // Form methods
  resetForm(): void {
    this.account = new Account();
    this.account.active = true;
    this.account.accountType = '';
    this.account.currentBalance = 0;
    this.account.openingBalance = 0;
    this.isEditMode = false;
  }

  resetTransactionForm(): void {
    this.transaction = new AccountTransaction();
    this.transactionAmount = 0;
    this.transactionDescription = '';
    this.referenceNo = '';
    this.thirdParty = '';
    this.selectedTransactionType = PaymentConstants.TRANSACTION_TYPE_CREDIT;
  }

  resetBalanceCorrectionForm(): void {
    this.correctionAmount = 0;
    this.correctionReason = '';
    this.selectedAccountForCorrection = null;
  }

  // CRUD operations
  saveAccount(): void {
    if (!this.validateAccountForm()) {
      return;
    }

    this.loading = true;
    this.accountService.save(this.account).subscribe(
      (response: Response) => {
        if (response.success) {
          this.notificationService.showSuccess(response.message);
          this.loadAccountList();
          this.closeModal();
        } else {
          this.notificationService.showError(response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error saving account:', error);
        this.notificationService.showError('Error saving account');
        this.loading = false;
      }
    );
  }

  deleteAccount(account: Account): void {
    if (confirm('Are you sure you want to delete this account?')) {
      account.active = false;
      this.accountService.save(account).subscribe(
        (response: Response) => {
          if (response.success) {
            this.notificationService.showSuccess('Account deleted successfully');
            this.loadAccountList();
          } else {
            this.notificationService.showError(response.message);
          }
        },
        (error) => {
          console.error('Error deleting account:', error);
          this.notificationService.showError('Error deleting account');
        }
      );
    }
  }

  // Transaction operations
  createTransaction(): void {
    if (!this.validateTransactionForm()) {
      return;
    }

    this.transaction.account = this.selectedAccount;
    this.transaction.transactionType = this.selectedTransactionType;
    this.transaction.amount = this.transactionAmount;
    this.transaction.description = this.transactionDescription;
    this.transaction.referenceNo = this.referenceNo;
    this.transaction.thirdParty = this.thirdParty;
    this.transaction.paymentType = PaymentConstants.BANK_TRANSACTION_DEPOSIT;
    this.transaction.referenceType = PaymentConstants.REFERENCE_TYPE_DEPOSIT;

    this.accountService.createTransaction(this.transaction).subscribe(
      (response: Response) => {
        if (response.success) {
          this.notificationService.showSuccess(response.message);
          this.loadTransactions(this.selectedAccount.id);
          this.loadAccountList(); // Refresh to update balances
          this.resetTransactionForm();
        } else {
          this.notificationService.showError(response.message);
        }
      },
      (error) => {
        console.error('Error creating transaction:', error);
        this.notificationService.showError('Error creating transaction');
      }
    );
  }

  // Validation methods
  validateAccountForm(): boolean {
    if (!this.account.accountType) {
      this.notificationService.showError('Account type is required');
      return false;
    }
    if (!this.account.accountNo || this.account.accountNo.trim() === '') {
      this.notificationService.showError('Account number is required');
      return false;
    }

    // Type-specific validations
    if (this.account.accountType === 'BANK' && (!this.account.bankName || this.account.bankName.trim() === '')) {
      this.notificationService.showError('Bank name is required for bank accounts');
      return false;
    }
    if (this.account.accountType === 'SUPPLIER' && (!this.account.supplierName || this.account.supplierName.trim() === '')) {
      this.notificationService.showError('Supplier name is required for supplier accounts');
      return false;
    }
    if (this.account.accountType === 'CUSTOMER' && (!this.account.customerName || this.account.customerName.trim() === '')) {
      this.notificationService.showError('Customer name is required for customer accounts');
      return false;
    }
    if ((this.account.accountType === 'CASH' || this.account.accountType === 'PETTY_CASH') &&
        (!this.account.accountName || this.account.accountName.trim() === '')) {
      this.notificationService.showError('Account name is required for cash accounts');
      return false;
    }

    return true;
  }

  validateTransactionForm(): boolean {
    if (!this.transactionAmount || this.transactionAmount <= 0) {
      this.notificationService.showError('Transaction amount must be greater than 0');
      return false;
    }
    if (!this.transactionDescription || this.transactionDescription.trim() === '') {
      this.notificationService.showError('Transaction description is required');
      return false;
    }
    if (!this.selectedTransactionType) {
      this.notificationService.showError('Transaction type is required');
      return false;
    }
    return true;
  }

  // Account type change handler
  onAccountTypeChange(): void {
    // Clear type-specific fields when account type changes
    this.account.bankName = '';
    this.account.branch = '';
    this.account.supplierName = '';
    this.account.supplierId = '';
    this.account.customerName = '';
    this.account.customerId = '';
    this.account.accountName = '';
  }

  // Utility methods
  getCurrentBalance(account: Account): number {
    return account.currentBalance || 0;
  }

  getAccountDisplayName(account: Account): string {
    if (!account) return '';

    switch (account.accountType) {
      case 'BANK':
        return account.bankName || 'Bank Account';
      case 'SUPPLIER':
        return account.supplierName || 'Supplier Account';
      case 'CUSTOMER':
        return account.customerName || 'Customer Account';
      case 'CASH':
      case 'PETTY_CASH':
        return account.accountName || 'Cash Account';
      default:
        return account.accountName || account.accountNo || 'Account';
    }
  }

  getAccountDetails(account: Account): string {
    if (!account) return '';

    switch (account.accountType) {
      case 'BANK':
        return account.branch || '';
      case 'SUPPLIER':
        return account.supplierId || '';
      case 'CUSTOMER':
        return account.customerId || '';
      default:
        return '';
    }
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'LKR',
      minimumFractionDigits: 2
    }).format(amount || 0);
  }

  getAccountTypeLabel(accountType: string): string {
    const type = this.accountTypes.find(t => t.value === accountType);
    return type ? type.label : accountType;
  }

  getTransactionTypeClass(transactionType: string): string {
    return transactionType === PaymentConstants.TRANSACTION_TYPE_CREDIT ? 'text-success' : 'text-danger';
  }

  // Search and filter methods
  get filteredAccounts(): Account[] {
    let filtered = this.accountList;

    if (this.searchTerm) {
      const term = this.searchTerm.toLowerCase();
      filtered = filtered.filter(account =>
        this.getAccountDisplayName(account).toLowerCase().includes(term) ||
        account.accountNo?.toLowerCase().includes(term) ||
        account.accountHolderName?.toLowerCase().includes(term) ||
        account.bankName?.toLowerCase().includes(term) ||
        account.supplierName?.toLowerCase().includes(term) ||
        account.customerName?.toLowerCase().includes(term) ||
        account.accountName?.toLowerCase().includes(term)
      );
    }

    if (this.selectedAccountType) {
      filtered = filtered.filter(account => account.accountType === this.selectedAccountType);
    }

    return filtered;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.selectedAccountType = '';
  }

  // Balance correction
  correctBalance(): void {
    if (!this.validateBalanceCorrectionForm()) {
      return;
    }

    this.loading = true;
    this.accountService.correctBalance(
      this.selectedAccountForCorrection.id,
      this.correctionAmount,
      this.correctionReason,
      'Admin' // TODO: Get current user
    ).subscribe(
      (response: Response) => {
        if (response.success) {
          this.notificationService.showSuccess(response.message);
          this.loadAccountList();
          this.closeModal();
        } else {
          this.notificationService.showError(response.message);
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error correcting balance:', error);
        this.notificationService.showError('Error correcting balance');
        this.loading = false;
      }
    );
  }

  validateBalanceCorrectionForm(): boolean {
    if (!this.correctionAmount || this.correctionAmount < 0) {
      this.notificationService.showError('Please enter a valid balance amount');
      return false;
    }
    if (!this.correctionReason || this.correctionReason.trim() === '') {
      this.notificationService.showError('Please provide a reason for balance correction');
      return false;
    }
    return true;
  }
}
