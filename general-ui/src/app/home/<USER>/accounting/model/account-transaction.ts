import { Account } from './account';

export class AccountTransaction {

  public id: string;

  public account: Account;

  public transactionType: string; // DEBIT, CREDIT

  public paymentType: string; // PURCHASE_PAYMENT, DEPOSIT, WITHDRAWAL, TRANSFER, FUND_TRANSFER

  public amount: number;

  public referenceNo: string; // Purchase Invoice No, Deposit Slip No, etc.

  public referenceType: string; // PURCHASE_INVOICE, DEPOSIT, WITHDRAWAL, FUND_TRANSFER

  public description: string;

  public thirdParty: string; // Supplier name, customer name, etc.

  public transactionDate: Date;

  public balanceAfterTransaction: number;

  // For fund transfers
  public transferToAccount: Account;

  public transferFromAccountId: string;

  public transferToAccountId: string;

  public active: boolean;

  public createdBy: string;

  public createdDate: Date;

  public lastModifiedBy: string;

  public lastModifiedDate: Date;
}
