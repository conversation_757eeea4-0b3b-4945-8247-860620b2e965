export class Liability {
  public id: string;
  public liabilityName: string;
  public liabilityType: string; // CURRENT, LONG_TERM
  public category: string; // Accounts Payable, Loans, Accruals, etc.
  public description: string;
  public creditorName: string;
  public originalAmount: number;
  public currentBalance: number;
  public interestRate: number;
  public startDate: Date;
  public dueDate: Date;
  public paymentTerms: string;
  public paymentFrequency: string; // MONTHLY, QUARTERLY, ANNUALLY
  public collateral: string;
  public guarantor: string;
  public status: string; // ACTIVE, PAID, OVERDUE, DEFAULTED
  public priority: string; // HIGH, MEDIUM, LOW
  public active: boolean;
  public createdDate: Date;
  public lastPaymentDate: Date;
  public nextPaymentDate: Date;
  
  constructor() {
    this.liabilityType = 'CURRENT';
    this.status = 'ACTIVE';
    this.priority = 'MEDIUM';
    this.active = true;
    this.interestRate = 0;
    this.paymentFrequency = 'MONTHLY';
    this.createdDate = new Date();
  }
}
