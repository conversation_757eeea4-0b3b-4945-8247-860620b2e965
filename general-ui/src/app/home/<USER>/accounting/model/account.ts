export class Account {

  public id: string;

  public accountName: string;

  public accountNo: string;

  public accountType: string; // BAN<PERSON>, SUPPLIER, CUSTOMER, CASH, PETTY_CASH

  // Bank specific fields
  public bankName: string;

  public branch: string;

  public accountHolderName: string;

  // Supplier/Customer specific fields
  public supplierId: string;

  public customerId: string;

  public supplierName: string;

  public customerName: string;

  // Financial fields
  public currentBalance: number;

  public openingBalance: number;

  public remark: string;

  public active: boolean;

  // Audit fields
  public createdBy: string;

  public createdDate: Date;

  public lastModifiedBy: string;

  public lastModifiedDate: Date;
}
