import {PettyCash} from "./petty-cash";
import {MetaData} from "../../../core/model/metaData";

export class PettyCashTransaction {
  id: string;
  pettyCash: PettyCash;
  transactionType: MetaData;
  expenseCategory: MetaData;
  amount: number;
  description: string;
  voucherNo: string;
  receivedBy: string;
  approvedBy: string;
  balanceAfter: number;
  operator: string;
  remark: string;
  createdDate: Date;
  createdBy: string;
  lastModifiedDate: Date;
  lastModifiedBy: string;
}
