export class Equity {
  public id: string;
  public equityType: string; // CAPITAL, RETAINED_EARNINGS, DRAWINGS
  public description: string;
  public ownerName: string;
  public amount: number;
  public transactionDate: Date;
  public transactionType: string; // INVESTMENT, WITHDRAWAL, PROFIT_RETENTION
  public referenceNumber: string;
  public notes: string;
  public fiscalYear: string;
  public quarter: string;
  public status: string; // ACTIVE, REVERSED
  public approvedBy: string;
  public active: boolean;
  public createdDate: Date;
  
  constructor() {
    this.equityType = 'CAPITAL';
    this.transactionType = 'INVESTMENT';
    this.status = 'ACTIVE';
    this.active = true;
    this.createdDate = new Date();
    this.transactionDate = new Date();
  }
}
