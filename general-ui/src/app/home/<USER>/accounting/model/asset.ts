export class Asset {
  public id: string;
  public assetName: string;
  public assetType: string; // CURRENT, FIXED
  public category: string; // Cash, Bank, Equipment, Furniture, etc.
  public description: string;
  public purchaseDate: Date;
  public purchaseValue: number;
  public currentValue: number;
  public depreciationRate: number;
  public depreciationMethod: string; // STRAIGHT_LINE, DECLINING_BALANCE
  public usefulLife: number; // in years
  public location: string;
  public serialNumber: string;
  public supplier: string;
  public warrantyExpiry: Date;
  public status: string; // ACTIVE, DISPOSED, UNDER_MAINTENANCE
  public active: boolean;
  public createdDate: Date;
  public lastUpdated: Date;
  
  constructor() {
    this.assetType = 'CURRENT';
    this.status = 'ACTIVE';
    this.active = true;
    this.depreciationMethod = 'STRAIGHT_LINE';
    this.depreciationRate = 0;
    this.usefulLife = 5;
    this.createdDate = new Date();
  }
}
