import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  standalone: false,
  templateUrl: '500.component.html',
  styles: [`
    .fas { margin-right: 8px; }
    .btn { margin: 0 8px; }
    .display-1 { font-size: 6rem; }
  `]
})
export class P500Component implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
    // You could add analytics tracking for 500 errors here
  }

  goToHome(): void {
    this.router.navigate(['/home/<USER>']);
  }

  goBack(): void {
    window.history.back();
  }
}
