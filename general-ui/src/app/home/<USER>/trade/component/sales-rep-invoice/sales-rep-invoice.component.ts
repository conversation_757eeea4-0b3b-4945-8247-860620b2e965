import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import { Router } from '@angular/router';
import {Customer} from "../../model/customer";
import {SalesInvoiceRecord} from "../../model/sales-invoice-record";
import {Stock} from "../../../inventory/model/stock";
import {SalesInvoice} from "../../model/sales-invoice";
import {MetaData} from "../../../../core/model/metaData";
import {CustomerService} from "../../service/customer.service";
import {ItemService} from "../../../inventory/service/item.service";
import {SalesInvoiceService} from "../../service/sales-invoice.service";
import {StockService} from "../../../inventory/service/stock.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {RouteService} from "../../../../admin/service/route.service";
import {Item} from "../../../inventory/model/item";
import {Route} from "../../../../admin/model/route";

@Component({
  standalone: false,
  selector: 'app-sales-rep-invoice',
  templateUrl: './sales-rep-invoice.component.html',
  styleUrls: ['./sales-rep-invoice.component.css']
})
export class SalesRepInvoiceComponent implements OnInit {
  // Current step in the wizard
  currentStep: number = 1;

  // Customer selection
  customers: Customer[] = [];
  filteredCustomers: Customer[] = [];
  selectedCustomer: Customer | null = null;
  customerSearchText: string = '';

  // Route selection
  routes: Route[] = [];
  selectedRoute: string;

  // Item selection
  items: Item[] = [];
  filteredItems: Item[] = [];
  itemSearchText: string = '';
  selectedItems: SalesInvoiceRecord[] = [];

  // Stock information
  stocks: Stock[] = [];

  // Invoice details
  invoice: SalesInvoice = new SalesInvoice();

  // Payment methods
  paymentMethods: MetaData[] = [];
  selectedPaymentMethod: MetaData | null = null;

  // UI state
  loading: boolean = false;
  errorMessage: string = '';
  successMessage: string = '';

  // Barcode scanner support
  @ViewChild('barcodeInput') barcodeInput: ElementRef;

  constructor(
    private router: Router,
    private customerService: CustomerService,
    private itemService: ItemService,
    private salesInvoiceService: SalesInvoiceService,
    private stockService: StockService,
    private metaDataService: MetaDataService,
    private routeService: RouteService
  ) { }

  ngOnInit(): void {
    this.initializeInvoice();
    this.loadCustomers();
    this.loadRoutes();
    this.loadItems();
    this.loadPaymentMethods();
  }

  /**
   * Initialize a new invoice
   */
  initializeInvoice(): void {
    this.invoice = new SalesInvoice();
    this.invoice.date = new Date().toISOString(); // Convert to string format
    this.invoice.subTotal = 0;
    this.invoice.totalAmount = 0;
    this.invoice.totalDiscount = 0;
    this.invoice.payment = 0;
    this.invoice.balance = 0;
    this.selectedItems = [];
  }

  /**
   * Load customers from the server
   */
  loadCustomers(): void {
    this.loading = true;
    this.customerService.findAllPagination(0, 1000).subscribe(
      (response: any) => {
        if (response && response.content) {
          this.customers = response.content;
          this.filteredCustomers = response.content;
        } else {
          this.customers = [];
          this.filteredCustomers = [];
        }
        this.loading = false;
      },
      error => {
        console.error('Error loading customers:', error);
        this.errorMessage = 'Failed to load customers. Please try again.';
        this.loading = false;
      }
    );
  }

  /**
   * Load routes from the server
   */
  loadRoutes(): void {
    this.loading = true;
    this.routeService.findAll(0, 1000).subscribe(
      (response: any) => {
        if (response && response.content) {
          this.routes = response.content;
        } else {
          this.routes = [];
        }
        this.loading = false;
      },
      error => {
        console.error('Error loading routes:', error);
        this.errorMessage = 'Failed to load routes. Please try again.';
        this.loading = false;
      }
    );
  }

  /**
   * Load items from the server
   */
  loadItems(): void {
    this.loading = true;
    this.itemService.findAll(0, 1000).subscribe(
      (response: any) => {
        if (response && response.content) {
          this.items = response.content;
          this.filteredItems = response.content;
        } else {
          this.items = [];
          this.filteredItems = [];
        }
        this.loadStocks();
      },
      error => {
        console.error('Error loading items:', error);
        this.errorMessage = 'Failed to load items. Please try again.';
        this.loading = false;
      }
    );
  }

  /**
   * Load stocks from the server
   */
  loadStocks(): void {
    this.stockService.findAll(0, 1000).subscribe(
      (response: any) => {
        if (response && response.content) {
          this.stocks = response.content;
        } else {
          this.stocks = [];
        }
        this.loading = false;
      },
      error => {
        console.error('Error loading stocks:', error);
        this.errorMessage = 'Failed to load stock information. Please try again.';
        this.loading = false;
      }
    );
  }

  /**
   * Load payment methods from the server
   */
  loadPaymentMethods(): void {
    this.metaDataService.findByCategory('PaymentMethod').subscribe(
      (data: any) => {
        this.paymentMethods = data;
      },
      error => {
        console.error('Error loading payment methods:', error);
        this.errorMessage = 'Failed to load payment methods. Please try again.';
      }
    );
  }

  /**
   * Filter customers by search text
   */
  filterCustomers(): void {
    if (!this.customerSearchText.trim()) {
      this.filteredCustomers = this.customers;
      return;
    }

    const searchText = this.customerSearchText.toLowerCase();
    this.filteredCustomers = this.customers.filter(customer =>
      customer.name.toLowerCase().includes(searchText) ||
      (customer.customerNo && customer.customerNo.toLowerCase().includes(searchText))
    );
  }

  /**
   * Filter items by search text
   */
  filterItems(): void {
    if (!this.itemSearchText.trim()) {
      this.filteredItems = this.items;
      return;
    }

    const searchText = this.itemSearchText.toLowerCase();
    this.filteredItems = this.items.filter(item =>
      item.itemName.toLowerCase().includes(searchText) ||
      (item.barcode && item.barcode.toLowerCase().includes(searchText))
    );
  }

  /**
   * Filter items by route
   */
  filterItemsByRoute(): void {
    if (!this.selectedRoute) {
      this.filteredItems = this.items;
      return;
    }

    // This is a placeholder - you would need to implement logic to filter items by route
    // based on your business rules (e.g., items commonly sold on this route)
    this.filteredItems = this.items;
  }

  /**
   * Select a customer
   */
  selectCustomer(customer: Customer): void {
    this.selectedCustomer = customer;
    this.invoice.customerNo = customer.customerNo;

    // If customer has a route, select it
    if (customer.routeNo) {
      this.selectedRoute = customer.routeNo;
      this.invoice.routeNo = customer.routeNo;
      this.filterItemsByRoute();
    }

    // Move to the next step
    this.nextStep();
  }

  /**
   * Add an item to the invoice
   */
  addItem(item: Item): void {
    // Check if item is already in the invoice
    const existingItem = this.selectedItems.find(record => record.itemCode === item.itemCode);

    if (existingItem) {
      // Increment quantity if already in the invoice
      existingItem.quantity += 1;
      this.updateItemTotal(existingItem);
    } else {
      // Add new item to the invoice
      const record = new SalesInvoiceRecord();
      record.itemCode = item.itemCode;
      record.itemName = item.itemName;
      record.barcode = item.barcode;
      record.quantity = 1;
      record.price = item.sellingPrice;
      record.unitPrice = item.sellingPrice;
      record.unitPriceOriginal = item.sellingPrice;
      // Use item.cost instead of costPrice
      record.itemCost = item.itemCost || 0;
      record.discount = 0;

      // If item manages stock, find and set the stock ID
      if (item.manageStock) {
        this.stockService.findMainStockByItemCodeAndPrice(item.itemCode, item.sellingPrice)
          .subscribe((stock: Stock) => {
            if (stock && stock.id) {
              record.stockId = stock.id;
            }
          }, error => {
            console.error('Error finding stock for item:', item.itemCode, error);
          });
      }

      this.selectedItems.push(record);
    }

    this.calculateInvoiceTotal();
  }

  /**
   * Remove an item from the invoice
   */
  removeItem(index: number): void {
    this.selectedItems.splice(index, 1);
    this.calculateInvoiceTotal();
  }

  /**
   * Update item quantity
   */
  updateQuantity(record: SalesInvoiceRecord, quantity: number): void {
    record.quantity = quantity;
    this.updateItemTotal(record);
    this.calculateInvoiceTotal();
  }

  /**
   * Update item discount
   */
  updateDiscount(record: SalesInvoiceRecord, discount: number): void {
    record.discount = discount;
    this.updateItemTotal(record);
    this.calculateInvoiceTotal();
  }

  /**
   * Update item total
   */
  updateItemTotal(record: SalesInvoiceRecord): void {
    // SalesInvoiceRecord doesn't have a total property, so we just calculate it when needed
    // The total is calculated as (price * quantity) - discount
  }

  /**
   * Calculate invoice total
   */
  calculateInvoiceTotal(): void {
    let subTotal = 0;
    let totalDiscount = 0;

    for (const item of this.selectedItems) {
      subTotal += item.price * item.quantity;
      totalDiscount += item.discount;
    }

    this.invoice.subTotal = subTotal;
    this.invoice.totalDiscount = totalDiscount;
    this.invoice.totalAmount = subTotal - totalDiscount;

    // Update balance based on payment
    this.updateBalance();
  }

  /**
   * Update balance based on payment
   */
  updateBalance(): void {
    this.invoice.balance = this.invoice.totalAmount - this.invoice.payment;
  }

  /**
   * Set payment method
   */
  setPaymentMethod(method: MetaData): void {
    this.selectedPaymentMethod = method;
    this.invoice.paymentMethod = method;

    // If cash payment, set payment to total amount
    if (method.value === 'Cash') {
      this.invoice.payment = this.invoice.totalAmount;
      this.invoice.balance = 0;
    }
  }

  /**
   * Move to the next step
   */
  nextStep(): void {
    this.currentStep++;
  }

  /**
   * Move to the previous step
   */
  prevStep(): void {
    this.currentStep--;
  }

  /**
   * Save the invoice
   */
  saveInvoice(): void {
    if (!this.validateInvoice()) {
      return;
    }

    this.loading = true;

    // Set the sales invoice records
    this.invoice.salesInvoiceRecords = this.selectedItems;

    // Save the invoice
    this.salesInvoiceService.save(this.invoice).subscribe(
      response => {
        this.loading = false;
        if (response.success) {
          this.successMessage = 'Invoice created successfully!';
          // Reset the form after a delay
          setTimeout(() => {
            this.initializeInvoice();
            this.currentStep = 1;
            this.successMessage = '';
          }, 2000);
        } else {
          this.errorMessage = response.message || 'Failed to create invoice. Please try again.';
        }
      },
      error => {
        console.error('Error saving invoice:', error);
        this.errorMessage = 'Failed to create invoice. Please try again.';
        this.loading = false;
      }
    );
  }

  /**
   * Validate the invoice before saving
   */
  validateInvoice(): boolean {
    // Check if customer is selected
    if (!this.selectedCustomer) {
      this.errorMessage = 'Please select a customer.';
      return false;
    }

    // Check if items are added
    if (this.selectedItems.length === 0) {
      this.errorMessage = 'Please add at least one item to the invoice.';
      return false;
    }

    // Check if payment method is selected
    if (!this.selectedPaymentMethod) {
      this.errorMessage = 'Please select a payment method.';
      return false;
    }

    return true;
  }

  /**
   * Get available stock for an item
   */
  getAvailableStock(itemCode: string): number {
    const stock = this.stocks.find(s => s.itemCode === itemCode);
    return stock ? stock.quantity : 0;
  }

  /**
   * Check if an item is in stock
   */
  isInStock(itemCode: string): boolean {
    return this.getAvailableStock(itemCode) > 0;
  }

  /**
   * Handle barcode scan
   */
  handleBarcodeScan(event: any): void {
    const barcode = event.target.value.trim();

    if (barcode) {
      // Find item by barcode
      const item = this.items.find(i => i.barcode === barcode);

      if (item) {
        this.addItem(item);
        // Clear the input
        event.target.value = '';
      }
    }
  }

  /**
   * Cancel the invoice creation
   */
  cancelInvoice(): void {
    if (confirm('Are you sure you want to cancel this invoice? All data will be lost.')) {
      this.initializeInvoice();
      this.currentStep = 1;
    }
  }
}
