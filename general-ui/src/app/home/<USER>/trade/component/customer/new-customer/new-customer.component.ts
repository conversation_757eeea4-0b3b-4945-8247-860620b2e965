import {Component, OnInit, ViewChild} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {NgForm} from '@angular/forms';
import {NotificationService} from '../../../../../core/service/notification.service';
import {Customer} from '../../../model/customer';
import {MetaDataService} from '../../../../../core/service/metaData.service';
import {CustomerService} from "../../../service/customer.service";
import {RouteService} from '../../../../../admin/service/route.service';
import {Response} from "../../../../../core/model/response";
import {Route} from "../../../../../admin/model/route";

@Component({
  standalone: false,
  selector: 'app-new-customer',
  templateUrl: './new-customer.component.html',
  styleUrls: ['./new-customer.component.css']
})

export class NewCustomerComponent implements OnInit {
  @ViewChild('photo', {static: true}) photo;

  customer: Customer;
  customerSearchList: Array<Customer> = [];
  nicAvailability = false;
  modalRef: BsModalRef;
  isEdit: boolean;
  isModal = false;
  type: string;
  salutations: Array<string>;
  routes: Array<Route> = [];
  routeSearchTerm: string = '';

  constructor(public customerService: CustomerService,
              public notificationService: NotificationService,
              public metaDataService: MetaDataService,
              private routeService: RouteService) {
  }

  ngOnInit() {
    // Only initialize a new customer if we're not in edit mode
    if (!this.customer || !this.customer.id) {
      this.customer = new Customer();
      this.customer.active = true;
    }

    this.salutations = ['Mr.', 'Mrs.', 'Ms.', 'BM.', 'AD.', 'CC.'];
    this.loadAllRoutes();

    // If we have a routeNo but routeSearchTerm is empty, try to find the route
    if (this.customer && this.customer.routeNo && (!this.routeSearchTerm || this.routeSearchTerm === "Not Set")) {
      this.findRouteByRouteNo(this.customer.routeNo);
    }
  }

  /**
   * Load all routes for the dropdown
   */
  loadAllRoutes() {
    this.routeService.findAll(0, 100).subscribe(
      (data: any) => {
        this.routes = data.content;
      },
      (error) => {
        console.error('Error loading routes:', error);
        this.notificationService.showError('Failed to load routes');
      }
    );
  }

  /**
   * Load routes based on search term
   */
  loadRoutes() {
    if (this.routeSearchTerm && this.routeSearchTerm.trim() !== '') {
      this.routeService.findByName(this.routeSearchTerm).subscribe(
        (data: Route[]) => {
          this.routes = data;
        },
        (error) => {
          console.error('Error searching routes:', error);
          this.notificationService.showError('Failed to search routes');
        }
      );
    } else {
      this.loadAllRoutes();
    }
  }

  /**
   * Find a route by its routeNo
   * @param routeNo The route number to search for
   */
  findRouteByRouteNo(routeNo: string) {
    // First try to find the route in the already loaded routes
    const foundRoute = this.routes.find(route => route.routeNo === routeNo);
    if (foundRoute) {
      this.routeSearchTerm = foundRoute.name;
      // Always update the customer's routeName to ensure consistency
      this.customer.routeName = foundRoute.name;
      console.log('Found route in loaded routes:', foundRoute);
      return;
    }

    // If not found, load all routes and try again
    this.routeService.findAll(0, 1000).subscribe(
      (data: any) => {
        if (data && data.content) {
          const allRoutes = data.content;
          const route = allRoutes.find(r => r.routeNo === routeNo);
          if (route) {
            this.routeSearchTerm = route.name;
            // Always update the customer's routeName to ensure consistency
            this.customer.routeName = route.name;
            console.log('Found route from API:', route);
          } else {
            console.log('Route not found for routeNo:', routeNo);
          }
        }
      },
      (error) => {
        console.error('Error finding route by routeNo:', error);
      }
    );
  }

  /**
   * Select a route from the typeahead
   */
  selectRoute(event: any) {
    if (event && event.item) {
      this.customer.routeNo = event.item.routeNo;
      this.customer.routeName = event.item.name; // Use the name property from the route
      console.log('Selected route:', event.item);
      console.log('Updated customer route info - routeNo:', this.customer.routeNo, 'routeName:', this.customer.routeName);
    }
  }

  gotoTop() {
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth'
    });
  }

  checkNic() {
    this.customerService.checkNic(this.customer.nicBr).subscribe((res: boolean) => {
      this.nicAvailability = res;
    });
  }

  savePerson(form: NgForm) {
    // Make sure routeName is set correctly if routeNo exists
    if (this.customer.routeNo && !this.customer.routeName && this.routes.length > 0) {
      const foundRoute = this.routes.find(route => route.routeNo === this.customer.routeNo);
      if (foundRoute) {
        this.customer.routeName = foundRoute.name;
      }
    }

    console.log('Saving customer with route info:', this.customer.routeNo, this.customer.routeName);

    this.customerService.save(this.customer).subscribe((result: Response) => {
      if (result.success) {
        this.notificationService.showSuccess('Customer Saved Successfully');
        if (this.isModal) {
          this.modalRef.hide();
        }
        form.reset();
        this.gotoTop();
        this.ngOnInit();
      } else if (!result.success) {
        this.notificationService.showError(result.message);
        this.gotoTop();
        form.reset();
        this.ngOnInit();
        this.customer.active = true;
      }
    })
  }

  clear() {
    // If in edit mode, just reset the route search term
    if (this.isEdit && this.customer && this.customer.id) {
      this.routeSearchTerm = this.customer.routeName || '';
    } else {
      // Otherwise, create a new customer
      this.customer = new Customer();
      this.customer.active = true;
      this.routeSearchTerm = '';
    }
  }

  setSelectedCustomer(event) {
    this.customer = event.item;
  }

  loadCustomerByNic() {
    if (this.customer.nicBr !== '') {
      this.customerService.findByNicLike(this.customer.nicBr).subscribe((data: Array<Customer>) => {
        this.customerSearchList = data;
      });
    } else {
      this.ngOnInit();
    }
  }
}

