import {Component, OnInit} from '@angular/core';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {Supplier} from '../../model/supplier';
import {NotificationService} from '../../../../core/service/notification.service';
import {ResponseHandlerService} from '../../../../core/service/response-handler.service';
import {SupplierService} from "../../service/supplier.service";

@Component({
  standalone: false,
  selector: 'app-supplier',
  templateUrl: './supplier.component.html',
  styleUrls: ['./supplier.component.css']
})
export class SupplierComponent implements OnInit {
  modalRef: BsModalRef;
  supplier = new Supplier();
  suppliers: Array<Supplier>;
  setClickedRow: Function;
  selectedRow: number;
  keySupplier: string;
  collectionSize;
  page;
  pageSize;

  selectedSupplier = new Supplier();

  // Flag to determine if component is opened as a modal
  isModal: boolean = false;

  constructor(private supplierService: SupplierService,
              private notificationService: NotificationService,
              private responseHandler: ResponseHandlerService) {
  }

  ngOnInit() {
    this.supplier = new Supplier();
    this.supplier.active = true;
    this.page = 1;
    this.pageSize = 8;
    this.suppliers = [];
    this.findAll();

    // If modalRef is set, then component is opened as a modal
    this.isModal = !!this.modalRef;
  }

  saveSupplier() {
    this.supplierService.save(this.supplier).subscribe(result => {
      this.responseHandler.handleSaveResponse(result, 'Supplier', () => {
        this.ngOnInit();
      });
    }, error => {
      this.responseHandler.handleError(error, 'Save supplier');
    });
  }

  loadSuppliers() {
    this.supplierService.findByNameLike(this.keySupplier).subscribe((data: Array<Supplier>) => {
      this.suppliers = data;
    });
  }

  findAll() {
    this.supplierService.findAll(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.suppliers = data.content;
      this.collectionSize = data.totalPages * 8;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  supplierDetail(supplier) {
    this.supplier = supplier;
  }

  setSelectedSupplier(event) {
    this.selectedSupplier = event.item;
    this.supplier = this.selectedSupplier;
    this.suppliers = [];
    this.suppliers.push(this.supplier);
  }

  Clear() {
    this.supplier = new Supplier();
    this.keySupplier = "";
  }

  /**
   * Closes the modal
   */
  closeModal() {
    if (this.modalRef) {
      this.modalRef.hide();
    }
  }

  /**
   * Sets the modalRef and updates the isModal flag
   * @param ref The modal reference
   */
  setModalRef(ref: BsModalRef) {
    this.modalRef = ref;
    this.isModal = true;
  }
}
