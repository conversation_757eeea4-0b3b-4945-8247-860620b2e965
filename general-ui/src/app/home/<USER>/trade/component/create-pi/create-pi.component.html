<div class="container-fluid px-0 p-1">
  <div class="theme-color">
    <div class="d-flex justify-content-between align-items-center header-section">
      <div>
        <h2 class="mb-0">CREATE PURCHASE INVOICE</h2>
      </div>
    </div>
    <!-- Mobile action buttons -->
    <div class="d-flex justify-content-between mt-2 d-md-none">
      <button class="btn btn-sm btn-secondary px-2" (click)="showSupplierModal()" title="Add/Select Supplier">
        <i class="fa fa-truck"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="showItemModal()" title="Add New Item">
        <i class="fa fa-plus-circle"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="openBarcodeScanner()" title="Scan Barcode">
        <i class="fa fa-barcode"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" (click)="clearTable()" title="Clear All Items">
        <i class="fa fa-trash"></i>
      </button>
      <button class="btn btn-sm btn-secondary px-2" routerLink="../home/<USER>" title="Home">
        <i class="fa fa-home"></i>
      </button>
    </div>
  </div>
  <div class="content-section overflow-auto">
    <form #piForm="ngForm" novalidate>
      <div class="p-0 m-0">
        <!-- Item search section - responsive layout -->
        <div class="row mt-0 mx-0">
          <!-- Barcode field -->
          <div class="form-group col-md-2 col-12 mb-2 px-1">
            <label>Barcode</label>
            <div class="input-group">
              <input #codeInput="ngModel" name="code" id="code"
                     (typeaheadLoading)="loadItemByCode()"
                     (typeaheadOnSelect)="setSelectedItem($event)"
                     [(ngModel)]="keyBarcodeSearch"
                     [typeaheadOptionsLimit]="15"
                     [typeahead]="itemSearchList"
                     autocomplete="off" class="form-control"
                     placeholder="Scan or Search Barcode"
                     type="text" typeaheadOptionField="barcode" typeaheadWaitMs="500">
              <div class="input-group-append">
                <button (click)="openBarcodeScanner()" class="btn btn-outline-primary" type="button" aria-label="Scan Barcode">
                  <i class="fa fa-barcode"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Item name field -->
          <div class="form-group col-md-4 col-12 mb-2 px-1">
            <label>Item Name</label>
            <div class="input-group">
              <input #nameInput="ngModel" name="name" id="name"
                     (typeaheadLoading)="searchItems()"
                     (typeaheadOnSelect)="setSelectedItem($event)"
                     [(ngModel)]="keyItemNameSearch"
                     [typeaheadOptionsLimit]="15"
                     [typeahead]="itemSearchList"
                     autocomplete="off" class="form-control"
                     placeholder="Search Item Name"
                     typeaheadOptionField="itemName" typeaheadWaitMs="500">
              <div class="input-group-append">
                <button (click)="showItemModal()" class="btn btn-outline-primary" type="button" aria-label="Add New Item">
                  <i class="fa fa-plus"></i>
                </button>
              </div>
            </div>
          </div>

          <!-- Cost field -->
          <div class="form-group col-md-2 col-6 mb-2 px-1">
            <label>Item Cost</label>
            <input #buyingPr="ngModel" [(ngModel)]="piRecord.itemCost"
                   [class.is-invalid]="buyingPr.invalid && (buyingPr.dirty || buyingPr.touched)"
                   class="form-control text-right" required type="number" min="0" step="0.01"
                   id="buyingPr" name="buyingPr" (ngModelChange)="setPercentage()">
          </div>

          <!-- Markup field -->
          <div class="form-group col-md-1 col-6 mb-2 px-1">
            <label>Markup (%)</label>
            <input #percentage="ngModel" (ngModelChange)="setSellingPrice()" [(ngModel)]="percentageValue"
                   class="form-control text-right" id="percentage" name="percentage" type="number" step="0.1">
          </div>

          <!-- Selling Price field -->
          <div class="form-group col-md-2 col-12 mb-2 px-1">
            <label>Selling Price</label>
            <input #sprice="ngModel" (ngModelChange)="setPercentage()" [(ngModel)]="piRecord.sellingPrice"
                   [class.is-invalid]="sprice.invalid && (sprice.dirty || sprice.touched)"
                   class="form-control text-right" required type="number" min="0" step="0.01"
                   id="sprice" name="sprice">
          </div>

          <!-- Quantity field -->
          <div class="form-group col-md-1 col-12 mb-2 px-1">
            <label>Quantity</label>
            <input #quantity="ngModel" [(ngModel)]="piRecord.quantity"
                   [class.is-invalid]="quantity.invalid && (quantity.dirty || quantity.touched)"
                   class="form-control text-right" required type="number" min="0.01" step="any"
                   id="quantity" name="quantity" (keydown.enter)="addEntry()">
          </div>

          <!-- Full width Add button for mobile only -->
          <button class="btn btn-primary w-100 mt-2 mb-2 d-md-none add-item-btn" type="button" (click)="addEntry()"
                  [disabled]="nameInput.invalid || buyingPr.invalid || sprice.invalid || quantity.invalid || !piRecord.item?.id">
            <i class="fa fa-plus-circle mr-2"></i> Add Item
          </button>
        </div>

        <!-- Serial Numbers Row - Only shown for items with manageSerial=true -->
        <div class="row mt-2 mx-0" *ngIf="piRecord.item && piRecord.item.manageSerial">
          <div class="col-12 px-1">
            <label>Serial Numbers (Enter one per line, must match quantity)</label>
            <textarea #serialNumbers="ngModel" [(ngModel)]="piRecord.serialNumbers"
                     [class.is-invalid]="serialNumbers.invalid && (serialNumbers.dirty || serialNumbers.touched)"
                     class="form-control" required rows="3"
                     id="serialNumbers" name="serialNumbers"
                     placeholder="Enter serial numbers (one per line)"></textarea>
            <div *ngIf="serialNumbers.invalid && (serialNumbers.dirty || serialNumbers.touched)" class="invalid-feedback">
              Serial numbers required for this item
            </div>
            <small class="form-text text-muted" *ngIf="piRecord.item && piRecord.item.manageSerial">
              This item requires serial numbers. Enter exactly {{piRecord.quantity}} serial numbers (one per line).
            </small>
          </div>
        </div>

        <div class="row p-0 m-0">
          <div class="col-md-12 p-0 m-0">
            <div class="table-height">
              <!-- Desktop table view -->
              <div class="table-responsive d-none d-md-block">
                <table class="table table-bordered">
                  <thead>
                  <tr>
                    <td style="width: 350px; !important;">Barcode</td>
                    <td>Item</td>
                    <td style="width: 150px; !important;">Cost</td>
                    <td style="width: 100px; !important;">Qty</td>
                    <td style="width: 150px; !important;">Total</td>
                    <td style="width: 12px; !important;"></td>
                  </tr>
                  </thead>
                  <tbody>
                  <tr *ngFor="let entry of piRecords; let i=index"
                      (click)="selectEntry(entry,i)" [class.active]="i === selectedRow">
                    <td>{{ entry.item?.barcode || 'N/A' }}</td>
                    <td>{{ entry.itemName }}</td>
                    <td>{{ entry.itemCost | number : '1.2-2' }}</td>
                    <td>{{ entry.quantity | number }}</td>
                    <td>{{ (entry.quantity * entry.itemCost) | number : '1.2-2' }}</td>
                    <td>
                      <button class="btn btn-danger btn-sm" (click)="removeStockRecord()">X</button>
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>

              <!-- Mobile card view -->
              <div class="d-md-none">
                <div class="card mb-2" *ngFor="let entry of piRecords; let i=index"
                     (click)="selectEntry(entry,i)" [ngClass]="{'border-primary': i === selectedRow}">
                  <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                      <div>
                        <h6 class="mb-1">{{ entry.itemName }}</h6>
                        <small class="text-muted">{{ entry.item?.barcode || 'N/A' }}</small>
                      </div>
                      <div class="text-right">
                        <div><strong>{{ entry.itemCost | number : '1.2-2' }}</strong></div>
                        <div>Qty: {{ entry.quantity }}</div>
                      </div>
                    </div>
                    <div class="text-right mt-2">
                      <button class="btn btn-danger btn-sm" (click)="removeStockRecord()">Remove</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Invoice details section - improved for mobile -->
        <div class="row mt-3 mx-0">
          <!-- Date section -->
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Date</label>
            <input #piDate="ngModel" [(ngModel)]="purchaseInvoice.date" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD', containerClass: 'theme-dark-blue' }"
                   [class.is-invalid]="piDate.invalid && (piDate.dirty || piDate.touched)" bsDatepicker
                   class="form-control" id="piDate" name="piDate" placeholder="Invoice Date" required type="text">
          </div>

          <!-- Invoice No section -->
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Invoice No</label>
            <input #invoiceNo="ngModel" [(ngModel)]="purchaseInvoice.invoiceNo"
                   [class.is-invalid]="invoiceNo.invalid && (invoiceNo.dirty || invoiceNo.touched)"
                   class="form-control" id="invoiceNo" name="invoiceNo" placeholder="Supplier Invoice No" required type="text">
          </div>

          <!-- Warehouse section -->
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Warehouse</label>
            <select class="form-control" required #selectedWh="ngModel" id="selectedWh"
                    [class.is-invalid]="selectedWh.invalid && (selectedWh.dirty || selectedWh.touched)" name="selectedWh"
                    [(ngModel)]="selectedWarehouse">
              <option [ngValue]="undefined" disabled>Select Warehouse</option>
              <option *ngFor="let wh of warehouses" [ngValue]="wh">{{wh.name}}</option>
            </select>
          </div>

          <!-- Supplier section -->
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Supplier</label>
            <div class="input-group">
              <input #supplierInput="ngModel" name="searchSupplier" id="searchSupplier"
                     (typeaheadLoading)="searchSuppliers()"
                     (typeaheadOnSelect)="setSelectedSupplier($event)"
                     [(ngModel)]="keySupplierSearch"
                     [typeaheadOptionsLimit]="15"
                     [typeahead]="supplierSearchList"
                     [class.is-invalid]="supplierInput.invalid && (supplierInput.dirty || supplierInput.touched) && !purchaseInvoice.supplier?.id"
                     autocomplete="off" class="form-control"
                     placeholder="Search or Select Supplier" required
                     typeaheadOptionField="name" typeaheadWaitMs="500">
              <div class="input-group-append">
                <button (click)="showSupplierModal()" class="btn btn-primary" type="button" aria-label="Add New Supplier">
                  <i class="fa fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Totals section -->
        <div class="row mt-2 mx-0">
          <div class="offset-md-3 col-12 col-md-9 px-1">
            <div class="row mx-0">
              <div class="col-4 mb-2 px-1">
                <label>Sub Total</label>
                <input class="form-control" [ngModel]="totalBuyingPrice | number" readonly>
              </div>
              <div class="col-4 mb-2 px-1">
                <label>Discount</label>
                <input class="form-control" [(ngModel)]="purchaseInvoice.discount" (ngModelChange)="calculateTotal()">
              </div>
              <div class="col-4 mb-2 px-1">
                <label>Total</label>
                <input class="form-control" [ngModel]="amountToBePaid | number" readonly>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment details section - improved for mobile -->
        <div class="row mt-2 mx-0">
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Due Date</label>
            <input #dueDate="ngModel" [(ngModel)]="purchaseInvoice.dueDate" [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD', containerClass: 'theme-dark-blue' }"
                   [class.is-invalid]="dueDate.invalid && (dueDate.dirty || dueDate.touched)" bsDatepicker
                   class="form-control" id="dueDate" name="dueDate" placeholder="Due Date" required type="text">
          </div>
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Payment</label>
            <input #paidAmount="ngModel" [(ngModel)]="purchaseInvoice.payment"
                   [class.is-invalid]="paidAmount.invalid && (paidAmount.dirty || paidAmount.touched)"
                   class="form-control" (ngModelChange)="calculateBalance()"
                   id="paidAmount" name="paidAmount" required type="number" min="0" step="0.01" placeholder="Amount paying">
          </div>
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Payment Method</label>
            <select class="form-control" required #selectedPm="ngModel" id="selectedPm"
                    [class.is-invalid]="selectedPm.invalid && (selectedPm.dirty || selectedPm.touched)" name="selectedPm"
                    [(ngModel)]="selectedPaymentMethod">
              <option [ngValue]="undefined">-Select-</option>
              <option *ngFor="let pm of paymentMethods" [ngValue]="pm">{{pm.value || pm.name}}</option>
            </select>
          </div>
          <div class="col-12 col-md-3 mb-2 px-1">
            <label>Balance</label>
            <input class="form-control" [ngModel]="purchaseInvoice.balance | number" readonly>
          </div>
        </div>

        <!-- Cheque Button Row -->
        <div class="row mt-2 mx-0" *ngIf="selectedPaymentMethod && selectedPaymentMethod.id === chequeId">
          <div class="col-12 text-right px-1">
            <button type="button" class="btn btn-outline-primary"
                    (click)="openChequePaymentModal()">
              <i class="fa fa-money-check mr-1"></i> {{ purchaseInvoice.cheque ? 'Edit' : 'Add' }} Cheque
            </button>
          </div>
        </div>

      </div>
    </form>

    <!-- Cheque Information -->
    <div class="row mt-2 mx-0" *ngIf="purchaseInvoice.cheque">
      <div class="col-12 px-1">
        <div class="alert alert-info d-flex justify-content-between align-items-center">
          <span>
            <i class="fa fa-money-check mr-2"></i>
            <strong>Cheque:</strong> {{ purchaseInvoice.cheque.chequeNo }} - {{ purchaseInvoice.cheque.chequeAmount | number:'1.2-2' }} - {{ purchaseInvoice.cheque.bank?.value }}
          </span>
          <button type="button" class="btn btn-sm btn-outline-danger" mwlConfirmationPopover
                  (confirm)="removeCheque()" [popoverMessage]="'Remove Cheque?'" title="Remove Cheque">
            <i class="fa fa-times"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- Action buttons - optimized for mobile -->
    <div class="mt-3">
      <!-- Desktop view buttons -->
      <div class="d-none d-md-flex justify-content-between align-items-center">
        <!-- Left aligned buttons -->
        <div>
          <button type="button" class="btn btn-outline-danger btn-lg mr-2" (click)="removeStockRecord()"
                  [disabled]="selectedRow === undefined" title="Remove Selected Item">
            <i class="fa fa-trash"></i> Remove Selected
          </button>
        </div>

        <!-- Right aligned buttons -->
        <div>
          <button type="button" class="btn btn-danger btn-lg mr-2" mwlConfirmationPopover (confirm)="clearPiRecord()" title="Clear">
            <i class="fa fa-trash"></i> Clear
          </button>
          <button type="button" class="btn btn-primary btn-lg mr-2" (click)="save(piForm)"
                  [disabled]="piForm.invalid || !piRecords || piRecords.length === 0" title="Save">
            <i class="fa fa-save"></i> Save
          </button>
          <button type="button" class="btn btn-primary btn-lg" (click)="save(piForm)"
                  [disabled]="piForm.invalid || !piRecords || piRecords.length === 0" title="Save & Print">
            <i class="fa fa-print"></i> Save & Print
          </button>
        </div>
      </div>

      <!-- Mobile view buttons - compact layout -->
      <div class="d-flex d-md-none justify-content-between">
        <div>
          <button type="button" class="btn btn-outline-danger btn-sm mx-1" (click)="removeStockRecord()"
                  [disabled]="selectedRow === undefined" title="Remove Selected Item">
            <i class="fa fa-trash"></i>
          </button>
        </div>
        <div>
          <button type="button" class="btn btn-danger btn-sm mx-1" mwlConfirmationPopover (confirm)="clearPiRecord()"
                  title="Clear">
            <i class="fa fa-trash"></i>
          </button>
          <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(piForm)"
                  [disabled]="piForm.invalid || !piRecords || piRecords.length === 0" title="Save">
            <i class="fa fa-save"></i>
          </button>
          <button type="button" class="btn btn-primary btn-sm mx-1" (click)="save(piForm)"
                  [disabled]="piForm.invalid || !piRecords || piRecords.length === 0" title="Save & Print">
            <i class="fa fa-print"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
