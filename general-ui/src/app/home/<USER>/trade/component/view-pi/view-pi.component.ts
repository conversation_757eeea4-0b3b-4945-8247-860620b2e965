import {Component, OnInit} from '@angular/core';
import {BsModalRef, BsModalService} from "ngx-bootstrap/modal";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {MetaData} from "../../../../core/model/metaData";
import {PurchaseInvoice} from "../../model/purchase-invoice";
import {PurchaseInvoiceService} from "../../service/purchase-invoice.service";

@Component({
  standalone: false,
  selector: 'app-view-si',
  templateUrl: './view-pi.component.html',
  styleUrls: ['./view-pi.component.css']
})
export class ViewPiComponent implements OnInit {

  purchaseInvoice: PurchaseInvoice;

  cashPayment: boolean;
  cash: MetaData;

  modalRef: BsModalRef;

  constructor(private modalService: BsModalService, private purchaseInvoiceService: PurchaseInvoiceService,
              private metaDataService: MetaDataService) {
  }

  ngOnInit(): void {
    this.purchaseInvoice = new PurchaseInvoice();
    this.purchaseInvoice.paymentMethod = new MetaData();
    this.purchaseInvoice.status = new MetaData();
    this.cashPayment = true;
    this.loadCashPayment();
  }

  findSi(invNo) {
    this.purchaseInvoiceService.findAllByInvoiceNo(invNo).subscribe((data: PurchaseInvoice) => {
      this.purchaseInvoice = data;
      this.checkPaymentMethod();
    })
  }

  loadCashPayment() {
    this.metaDataService.findByValueAndCategory("Cash", "PaymentMethod").subscribe((data: MetaData) => {
      this.cash = data;
    });
  }

  checkPaymentMethod() {
    if (this.purchaseInvoice.paymentMethod.id === this.cash.id) {
      this.cashPayment = false;
    } else {
      this.cashPayment = true;
    }
  }

  close() {
    this.modalRef.hide();
  }
}
