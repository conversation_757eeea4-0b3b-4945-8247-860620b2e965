import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { CashDrawer } from '../../model/cashDrawer';
import { User } from '../../../../admin/model/user';
import { Route } from '../../../../admin/model/route';
import { CashDrawerService } from '../../service/cashDrawer.service';
import { UserService } from '../../../../admin/service/user.service';
import { RouteService } from '../../../../admin/service/route.service';

@Component({
  standalone: false,
  selector: 'app-manage-si-more-filters-modal',
  templateUrl: './manage-si-more-filters-modal.component.html',
  styleUrls: ['./manage-si-more-filters-modal.component.css']
})
export class ManageSiMoreFiltersModalComponent implements OnInit {
  // Form
  filterForm: FormGroup;

  // Data
  cashDrawers: CashDrawer[] = [];
  cashierUsers: User[] = [];
  routes: Route[] = [];

  // Loading state
  loading: boolean = false;

  // Selected values (for returning to parent component)
  selectedCashDrawer: CashDrawer | null = null; // This is a Cash Drawer (device)
  selectedCashierUser: User | null = null; // This is a User with cashier role
  selectedRoute: Route | null = null;

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private cashDrawerService: CashDrawerService,
    private userService: UserService,
    private routeService: RouteService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      cashDrawerNo: [null],
      cashierUserId: [null],
      routeNo: [null]
    });
  }

  ngOnInit(): void {
    this.loadCashiers();
    this.loadRoutes();
  }

  /**
   * Load all cash drawers and users with cashier role
   */
  loadCashiers(): void {
    this.loading = true;

    // Load users with cashier role
    this.loadCashierUsers();

    // Load cash drawers
    this.loadCashDrawers();
  }

  /**
   * Load users with cashier role
   */
  loadCashierUsers(): void {
    this.userService.findUsersWithCashierRole().subscribe(
      (data: User[]) => {
        this.cashierUsers = data || [];

        // Check if loading is complete
        if (this.cashDrawers.length > 0) {
          this.loading = false;
        }
      },
      error => {
        console.error('Error loading cashier users:', error);
        this.toastr.error('Failed to load users with cashier role', 'Error');
        this.cashierUsers = []; // Ensure cashierUsers is always an array
        this.loading = false;
      }
    );
  }

  /**
   * Load all cash drawers
   */
  loadCashDrawers(): void {
    this.cashDrawerService.findAllCashDrawers().subscribe(
      (data: CashDrawer[]) => {
        this.cashDrawers = data || [];

        // Check if loading is complete
        if (this.cashierUsers.length > 0) {
          this.loading = false;
        }
      },
      error => {
        console.error('Error loading cash drawers:', error);
        this.toastr.error('Failed to load cash drawers', 'Error');
        this.cashDrawers = []; // Ensure cashDrawers is always an array
        this.loading = false;
      }
    );
  }

  /**
   * Load all routes
   */
  loadRoutes(): void {
    this.routeService.findAll().subscribe(
      (data: any) => {
        // Handle paginated response - extract content array
        this.routes = data.content || data || [];
      },
      error => {
        console.error('Error loading routes:', error);
        this.toastr.error('Failed to load routes', 'Error');
        this.routes = []; // Ensure routes is always an array
      }
    );
  }

  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    const cashDrawerNo = this.filterForm.get('cashDrawerNo')?.value;
    const cashierUserName = this.filterForm.get('cashierUserId')?.value;
    const routeNo = this.filterForm.get('routeNo')?.value;

    // Find selected cash drawer
    if (cashDrawerNo) {
      this.selectedCashDrawer = this.cashDrawers.find(c => c.drawerNo === cashDrawerNo) || null;
      // If cash drawer is selected, clear user selection
      this.selectedCashierUser = null;
    } else {
      this.selectedCashDrawer = null;
    }

    // Find selected user (only if no cash drawer is selected)
    if (cashierUserName && !this.selectedCashDrawer) {
      this.selectedCashierUser = this.cashierUsers.find(u => u.username === cashierUserName) || null;
    } else if (this.selectedCashDrawer) {
      // Clear user selection if cash drawer is selected
      this.selectedCashierUser = null;
    }

    // Find selected route
    if (routeNo) {
      this.selectedRoute = this.routes.find(r => r.routeNo === routeNo) || null;
    } else {
      this.selectedRoute = null;
    }

    this.modalRef.hide();
  }

  /**
   * Clear filters
   */
  clearFilters(): void {
    this.filterForm.reset();
    this.selectedCashDrawer = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;
  }
}
