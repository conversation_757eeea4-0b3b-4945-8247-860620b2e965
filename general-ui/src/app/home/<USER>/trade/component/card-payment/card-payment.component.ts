import {Component, OnInit} from '@angular/core';
import {BsModalRef} from "ngx-bootstrap/modal";
import {NotificationService} from "../../../../core/service/notification.service";
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {Cheque} from "../../model/cheque";
import {ChequeService} from "../../service/cheque.service";
import {Response} from "../../../../core/model/response";
import {Customer} from "../../model/customer";
import {CustomerService} from "../../service/customer.service";

@Component({
  standalone: false,
  selector: 'app-expense-type',
  templateUrl: './card-payment.component.html',
  styleUrls: ['./card-payment.component.css']
})
export class CardPaymentComponent implements OnInit {

  modalRef: BsModalRef;

  keyCustomer: string;
  customerList: Array<Customer>;

  payment: number;
  chequeAmount: number;
  cashAmount: number;
  chequeCardNo: string;
  chequeDate: Date;
  bankList: Array<MetaData>;
  bankId: string;
  cheque: Cheque;

  constructor(private notificationService: NotificationService, private metaDataService: MetaDataService,
              private chequeService: ChequeService, private customerService: CustomerService) {
  }

  ngOnInit() {
    this.chequeDate = new Date();
    this.loadBanks();
    this.cheque = new Cheque();
  }

  setPayment() {
    if(this.payment == null || this.payment == 0){
      this.notificationService.showError("Please Add Paying Amount First");
      return;
    }
    if ((this.chequeAmount + this.cashAmount) != this.payment) {
      this.notificationService.showError("Please Check Cash /Card Amounts Again");
      return;
    } else {
      this.chequeService.save(this.cheque).subscribe((res: Response)=>{
        if (res.code === 200){
          this.notificationService.showSuccess(res.message);
        }else {
          this.notificationService.showError(res.message);
        }
      });
      this.modalRef.hide();
    }
  }

  closeWindow() {
    this.modalRef.hide();
    this.payment = 0;
    this.chequeAmount = 0;
    this.cashAmount = 0;
    this.chequeCardNo = '';
  }

  setBank(event) {
    this.cheque.bank = new MetaData();
    this.cheque.bank.id = event.target.value;
  }

  loadBanks() {
    this.metaDataService.findByCategory("Bank").subscribe((res: Array<MetaData>)=>{
      this.bankList = res;
    })
  }

  setChequeNo(){
    this.chequeCardNo = this.cheque.chequeNo;
  }

  setAmount() {
    this.cheque.chequeAmount = this.chequeAmount;
  }

  loadCustomer() {
    this.customerService.findByNameLike(this.keyCustomer).subscribe((data: Array<Customer>)=>{
      this.customerList = data;
      console.log(data);
    })
  }

  setSelectedCustomer(event) {
    this.cheque.customer = new Customer();
    this.cheque.customer.id = event.item.id;
  }
}
