import {APP_INITIALIZER, NgModule} from '@angular/core';
import {FormsModule} from '@angular/forms';
import {TagInputModule} from 'ngx-chips';
import {ToastrModule} from 'ngx-toastr';
import {ConfirmationPopoverModule} from 'angular-confirmation-popover';
import {BsDatepickerModule} from 'ngx-bootstrap/datepicker';
import {BsDropdownModule} from 'ngx-bootstrap/dropdown';
import {ModalModule} from 'ngx-bootstrap/modal';
import {PaginationModule} from 'ngx-bootstrap/pagination';
import {TimepickerModule} from 'ngx-bootstrap/timepicker';
import {TypeaheadModule} from 'ngx-bootstrap/typeahead';
import {NgxPrintModule} from 'ngx-print';
import {TranslateService} from '../../translate.service';
import {TranslatePipe} from '../../translate.pipe';
import {CommonModule, DatePipe} from '@angular/common';
import {HTTP_INTERCEPTORS, HttpClientModule} from '@angular/common/http';
import {JwtInterceptor} from '../../helper/jwt.interceptor';
import {ErrorInterceptor} from '../../helper/error.interceptor';
import {RouterModule} from '@angular/router';
import {routeParams} from './core-routing.module';
import {NgxBarcode6Module} from 'ngx-barcode6';
import {ReactiveFormsModule} from '@angular/forms';
import {CollapseModule} from 'ngx-bootstrap/collapse';
import {NgxSpinnerModule} from "ngx-spinner";

export function setupTranslateFactory(
  service: TranslateService) {
  return () => {
    // Use the language from localStorage or default to English
    const lang = localStorage.getItem('lang') || 'en';
    console.log(`Initializing application with language: ${lang}`);

    // Load the translations
    return service.use(lang).then(() => {
      console.log('Translations loaded successfully');
      // Disable translation warnings by default (false parameter)
      service.debugTranslations(false);
    }).catch(error => {
      console.error('Error loading translations:', error);
    });
  };
}

@NgModule({
  declarations: [TranslatePipe, routeParams],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ModalModule.forRoot(),
    BsDatepickerModule.forRoot(),
    TypeaheadModule.forRoot(),
    ToastrModule.forRoot(),
    BsDropdownModule.forRoot(),
    TimepickerModule.forRoot(),
    HttpClientModule,
    TagInputModule,
    PaginationModule.forRoot(),
    RouterModule,
    NgxBarcode6Module,
    NgxPrintModule,
    NgxSpinnerModule,
    CollapseModule.forRoot(),
    ConfirmationPopoverModule.forRoot({
      confirmButtonType: 'danger',
      popoverTitle: 'Confirmation',
      popoverMessage: 'Are you sure?'
    })
  ],
  exports: [TranslatePipe,
    FormsModule,
    ReactiveFormsModule,
    PaginationModule,
    TagInputModule,
    ModalModule,
    TypeaheadModule,
    ToastrModule,
    BsDatepickerModule,
    HttpClientModule,
    BsDropdownModule,
    TimepickerModule,
    NgxPrintModule,
    NgxBarcode6Module,
    CollapseModule,
    NgxSpinnerModule,
    RouterModule, CommonModule, routeParams,
    ConfirmationPopoverModule
  ],
  providers: [{provide: HTTP_INTERCEPTORS, useClass: JwtInterceptor, multi: true},
    {provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true},
    TranslateService,DatePipe,
    {
      provide: APP_INITIALIZER,
      useFactory: setupTranslateFactory,
      deps: [TranslateService],
      multi: true
    }
  ]
})

export class CoreModule {
}
