import {NgModule} from '@angular/core';
import {RouterModule, Routes} from '@angular/router';
import {ReorderReportComponent} from "./components/reorder-report/reorder-report.component";
import {CompleteSummaryReportComponent} from "./components/complete-summary-report/complete-summary-report.component";
import {CreditReportComponent} from "./components/credit-report/credit-report.component";
import {StockReportComponent} from "./components/stock-report/stock-report.component";
import {ProfitReportComponent} from "./components/profit-report/profit-report.component";
import {ExpenseReportComponent} from "./components/expense-report/expense-report.component";
import {CashInOutReportComponent} from "./components/cash-in-out-report/cash-in-out-report.component";
import {
  ItemSalesSummaryReportComponent
} from "./components/item-sales-summary-report/item-sales-summary-report.component";
import {StockMovingReportComponent} from "./components/stock-moving-report/stock-moving-report.component";
import {SalesInvoiceReportComponent} from "./components/sales-invoice-report/sales-invoice-report.component";
import {MoreFiltersModalComponent} from "./components/sales-invoice-report/more-filters-modal/more-filters-modal.component";
import {ActionReportComponent} from "./components/action-report/action-report.component";
import {SupplierReturnReportComponent} from "./components/supplier-return-report/supplier-return-report.component";
import {ItemReportComponent} from "./components/item-report/item-report.component";
import {ItemSalesMoreFiltersModalComponent} from './components/item-sales-summary-report/more-filters-modal/more-filters-modal.component';
import {ProfitReportMoreFiltersModalComponent} from './components/profit-report/more-filters-modal/more-filters-modal.component';
import {StockReportFilterModalComponent} from './components/stock-report/filter-modal/filter-modal.component';
import {ItemReportFilterModalComponent} from './components/item-report/filter-modal/filter-modal.component';

const routes: Routes = [
  {
    path: 'action_report',
    component: ActionReportComponent
  },
  {
    path: 'supplier_return_report',
    component: SupplierReturnReportComponent
  },
  {
    path: 'stock_report',
    component: StockReportComponent
  },
  {
    path: 'stock_movement_report',
    component: StockMovingReportComponent
  },
  {
    path: 'reorder_report',
    component: ReorderReportComponent
  },
  {
    path: 'complete_summary_report',
    component: CompleteSummaryReportComponent
  },
  {
    path: 'item_sales_summary_report',
    component: ItemSalesSummaryReportComponent
  },
  {
    path: 'credit_report',
    component: CreditReportComponent
  },
  {
    path: 'profit_report',
    component: ProfitReportComponent
  },
  {
    path: 'expense_report',
    component: ExpenseReportComponent
  },
  {
    path: 'cashier_report',
    component: CashInOutReportComponent
  },
  {
    path: 'sales_invoice_report',
    component: SalesInvoiceReportComponent
  },
  {
    path: 'item_report',
    component: ItemReportComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class ReportRoutingModule {
}

export const reportRouteParams = [ReorderReportComponent, CompleteSummaryReportComponent, CreditReportComponent,
  ExpenseReportComponent, StockReportComponent, ProfitReportComponent, CashInOutReportComponent,
  ItemSalesSummaryReportComponent, StockMovingReportComponent, SalesInvoiceReportComponent,
  MoreFiltersModalComponent, ActionReportComponent, SupplierReturnReportComponent, ItemReportComponent,
  ItemSalesMoreFiltersModalComponent, ProfitReportMoreFiltersModalComponent, StockReportFilterModalComponent,
  ItemReportFilterModalComponent];
