import {Component, OnInit, OnDestroy} from '@angular/core';
import {Subscription} from 'rxjs';
import {MetaData} from "../../../../core/model/metaData";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {SalesInvoiceRecordService} from "../../../trade/service/sales-invoice-record.service";
import {ItemSaleSummaryAggr} from "../../../trade/model/itemSaleSummaryAggr";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";
import {ProfitReportMoreFiltersModalComponent} from "./more-filters-modal/more-filters-modal.component";
import {CashDrawer} from "../../../trade/model/cashDrawer";
import {User} from "../../../../admin/model/user";
import {Route} from "../../../../admin/model/route";
import {ToastrService} from "ngx-toastr";

@Component({
  standalone: false,
  selector: 'app-profit-report',
  templateUrl: './profit-report.component.html',
  styleUrls: ['./profit-report.component.css']
})
export class ProfitReportComponent implements OnInit, OnDestroy {

  sDate: Date;
  eDate: Date;
  durationFilter: Array<MetaData>;
  selectedDuration: MetaData;
  itemSaleSummary: Array<ItemSaleSummaryAggr>;
  selectedRow: number;
  totalAmount: number;
  selectedProfitType: string = 'realized'; // Default to realized profit
  loading: boolean = false;

  // Modal reference
  modalRef: BsModalRef;

  // Subscription management
  private subscriptions: Subscription = new Subscription();

  // Selected filter values
  selectedCashDrawer: CashDrawer | null = null;
  selectedCashierUser: User | null = null;
  selectedRoute: Route | null = null;

  constructor(private salesInvoiceRecordService: SalesInvoiceRecordService,
              private metaDataService: MetaDataService,
              private modalService: BsModalService,
              private toastr: ToastrService) {
  }

  ngOnInit(): void {
    this.selectedDuration = new MetaData();
    this.sDate = new Date();
    this.eDate = new Date();
    this.itemSaleSummary = [];
    this.totalAmount = 0;
    this.selectedProfitType = 'realized';
    this.findDurationFilterData();
    this.findTodayRecords();
  }

  /**
   * Find sales invoice records by duration ID
   * @param durationId Duration ID from metadata
   */
  findSalesInvoiceRecords(durationId) {
    this.loading = true;
    this.itemSaleSummary = [];

    // Get the metadata to determine date range
    this.metaDataService.findById(durationId).subscribe((range: MetaData) => {
      let sDate = new Date();
      let eDate = new Date();
      const today = new Date();

      if (range.value === "Today") {
        sDate = today;
        eDate = today;
      } else if (range.value === "This Week") {
        // Set to Monday of current week
        const day = today.getDay();
        const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Sunday
        sDate = new Date(today.setDate(diff));
        // Set to Sunday of current week
        eDate = new Date(sDate);
        eDate.setDate(sDate.getDate() + 6);
      } else if (range.value === "This Month") {
        sDate = new Date(today.getFullYear(), today.getMonth(), 1);
        eDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      } else if (range.value === "This Year") {
        sDate = new Date(today.getFullYear(), 0, 1);
        eDate = new Date(today.getFullYear(), 11, 31);
      }

      // Format dates for API
      const startDate = sDate.toLocaleDateString();
      const endDate = eDate.toLocaleDateString();

      // Get profit based on selected type
      const unrealized = this.selectedProfitType === 'unrealized';

      // Use the findProfitByRangeFilter method with the unrealized flag
      this.salesInvoiceRecordService.findByRangeFilter(durationId, unrealized).subscribe(
        (result: Array<ItemSaleSummaryAggr>) => {
          this.itemSaleSummary = result || [];
          this.calculateTotalAmount();
          this.loading = false;
        },
        error => {
          this.loading = false;
          this.toastr.error('Failed to fetch data', 'Error');
        }
      );
    });
  }

  findTodayRecords() {
    this.metaDataService.findByValueAndCategory("Today", "ReportFilterDuration").subscribe((result: MetaData) => {
      this.findSalesInvoiceRecords(result.id);
    })
  }

  findDurationFilterData() {
    this.metaDataService.findByCategory('ReportFilterDuration').subscribe((data: Array<MetaData>) => {
      this.durationFilter = data;
    });
  }

  /**
   * Search for profit data between selected dates
   */
  searchBetweenDates() {
    const startDate = this.sDate.toLocaleDateString();
    const endDate = this.eDate.toLocaleDateString();

    this.loading = true;
    this.itemSaleSummary = [];
    const unrealized = this.selectedProfitType === 'unrealized';

    // Determine which API method to call based on selected filters
    if (this.selectedCashDrawer) {
      // Filter by cash drawer with unrealized parameter
      this.salesInvoiceRecordService.findSalesByCashier(this.selectedCashDrawer.drawerNo, startDate, endDate, unrealized).subscribe(
        (result: Array<ItemSaleSummaryAggr>) => {
          this.itemSaleSummary = result || [];
          this.calculateTotalAmount();
          this.loading = false;
          this.toastr.info(`Found ${this.itemSaleSummary.length} records for cash drawer ${this.selectedCashDrawer.drawerNo}`, 'Filters Applied');
        },
        error => {
          this.loading = false;
          this.toastr.error('Failed to fetch data', 'Error');
        }
      );
    } else if (this.selectedCashierUser) {
      // Filter by cashier user with unrealized parameter
      this.salesInvoiceRecordService.findSalesByUser(this.selectedCashierUser.username, startDate, endDate, unrealized).subscribe(
        (result: Array<ItemSaleSummaryAggr>) => {
          this.itemSaleSummary = result || [];
          this.calculateTotalAmount();
          this.loading = false;
          this.toastr.info(`Found ${this.itemSaleSummary.length} records for user ${this.selectedCashierUser.username}`, 'Filters Applied');
        },
        error => {
          this.loading = false;
          this.toastr.error('Failed to fetch data', 'Error');
        }
      );
    } else if (this.selectedRoute) {
      // Filter by route with unrealized parameter
      this.salesInvoiceRecordService.findSalesByRoute(this.selectedRoute.routeNo, startDate, endDate, unrealized).subscribe(
        (result: Array<ItemSaleSummaryAggr>) => {
          this.itemSaleSummary = result || [];
          this.calculateTotalAmount();
          this.loading = false;
          this.toastr.info(`Found ${this.itemSaleSummary.length} records for route ${this.selectedRoute.name}`, 'Filters Applied');
        },
        error => {
          this.loading = false;
          this.toastr.error('Failed to fetch data', 'Error');
        }
      );
    } else {
      // No filters selected, use the findProfitBetween method with unrealized flag
      this.salesInvoiceRecordService.findProfitBetween(startDate, endDate, unrealized).subscribe(
        (result: Array<ItemSaleSummaryAggr>) => {
          this.itemSaleSummary = result || [];
          this.calculateTotalAmount();
          this.loading = false;
        },
        error => {
          this.loading = false;
          this.toastr.error('Failed to fetch data', 'Error');
        }
      );
    }
  }

  filterByDuration() {
    this.findSalesInvoiceRecords(this.selectedDuration.id);
  }

  /**
   * Calculate total profit amount from all items
   */
  calculateTotalAmount() {
    this.totalAmount = 0;

    if (!this.itemSaleSummary || this.itemSaleSummary.length === 0) {
      return;
    }

    for (let rec of this.itemSaleSummary) {
      if (rec && rec.profit != null) {
        this.totalAmount = this.totalAmount + rec.profit;
      }
    }
  }

  /**
   * Open more filters modal
   */
  openMoreFiltersModal(): void {
    this.modalRef = this.modalService.show(ProfitReportMoreFiltersModalComponent, <ModalOptions>{ class: 'modal-md' });

    // Subscribe to modal close event to get selected filters
    this.modalRef.content.modalRef = this.modalRef;

    // When modal is closed, update filters
    const modalSubscription = this.modalService.onHide.subscribe(() => {
      if (this.modalRef && this.modalRef.content) {
        // Get selected filters from modal
        this.selectedCashDrawer = this.modalRef.content.selectedCashDrawer;
        this.selectedCashierUser = this.modalRef.content.selectedCashierUser;
        this.selectedRoute = this.modalRef.content.selectedRoute;
      }
    });

    // Add the subscription to our collection for cleanup
    this.subscriptions.add(modalSubscription);
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.selectedCashDrawer = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;

    // Reset date range to today
    this.sDate = new Date();
    this.eDate = new Date();

    // Find today's records
    this.findTodayRecords();
  }

  /**
   * Handle profit type change
   */
  onProfitTypeChange(): void {
    // Refresh data with the new profit type
    if (this.selectedDuration && this.selectedDuration.id) {
      this.filterByDuration();
    } else {
      this.searchBetweenDates();
    }
  }

  /**
   * Format date for display
   * @param date Date to format
   * @returns Formatted date string
   */
  formatDateForDisplay(date: Date): string {
    if (!date) return '';
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  }

  /**
   * Get current date formatted for display
   * @returns Formatted date string
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Clean up subscriptions on component destruction
   */
  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
  }
}
