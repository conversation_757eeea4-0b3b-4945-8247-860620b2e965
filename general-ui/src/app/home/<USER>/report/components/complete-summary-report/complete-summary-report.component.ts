import {Component, OnInit} from '@angular/core';
import {MetaData} from "../../../../core/model/metaData";
import {Transaction} from "../../../../core/model/transaction";
import {TransactionService} from "../../../../core/service/transaction.service";
import {MetaDataService} from "../../../../core/service/metaData.service";
import {Invoice80Component} from "../../../trade/component/invoices/invoice-80-en/invoice-80.component";
import {BsModalRef, BsModalService, ModalOptions} from "ngx-bootstrap/modal";

@Component({
  standalone: false,
  selector: 'app-income-report',
  templateUrl: './complete-summary-report.component.html',
  styleUrls: ['./complete-summary-report.component.css']
})
export class CompleteSummaryReportComponent implements OnInit {

  sDate: Date;
  eDate: Date;
  durationFilter: Array<MetaData>;
  selectedDuration: MetaData;
  transactions: Array<Transaction>;
  selectedTr: Transaction;
  selectedRow: number;
  totalAmount: number;
  modalRef: BsModalRef;
  public loading = false;

  constructor(private transactionService: TransactionService, private metaDataService: MetaDataService,
              private modalService: BsModalService) {
  }

  ngOnInit(): void {
    this.selectedDuration = new MetaData();
    this.sDate = new Date();
    this.eDate = new Date();
    this.findDurationFilterData();
    this.findTodayTransaction();
    this.setupScrollListener();
  }

  /**
   * Setup scroll listener to ensure footer remains visible when scrolling to bottom
   * but with slight transparency to see content underneath if needed
   */
  setupScrollListener(): void {
    window.addEventListener('scroll', () => {
      const footer = document.querySelector('.fixed-bottom');
      if (footer) {
        const scrollPosition = window.scrollY + window.innerHeight;
        const documentHeight = document.body.scrollHeight;

        // When close to bottom, make footer slightly transparent
        // but still visible enough to use the buttons
        if (documentHeight - scrollPosition < 100) {
          (footer as HTMLElement).style.opacity = '0.9';
        } else {
          (footer as HTMLElement).style.opacity = '1';
        }
      }
    });
  }

  findTransaction(durationId) {
    this.loading = true;
    // Use findAllByDateRange instead of filtering by operator to show all transactions
    this.transactionService.findByRangeFilter(durationId).subscribe(
      (result: Array<Transaction>) => {
        this.transactions = result;
        this.calculateTotalAmount();
        this.loading = false;
      })
  }

  findTodayTransaction() {
    this.loading = true;
    this.metaDataService.findByValueAndCategory("Today", "ReportFilterDuration").subscribe((result: MetaData) => {
      this.findTransaction(result.id);
      this.loading = false;
    })
  }

  findDurationFilterData() {
    this.metaDataService.findByCategory('ReportFilterDuration').subscribe((data: Array<MetaData>) => {
      this.durationFilter = data;
    });
  }

  searchBetweenDates() {
    this.loading = true;
    // Use findByDateRange instead of filtering by operator to show all transactions
    this.transactionService.findByDateRange(this.sDate.toLocaleDateString(), this.eDate.toLocaleDateString()).subscribe(
      (result: Array<Transaction>) => {
        this.transactions = result;
        this.calculateTotalAmount();
        this.loading = false;
      })
  }

  filterByDuration() {
    this.findTransaction(this.selectedDuration.id);
  }

  selectTr(tr, index) {
    this.selectedTr = tr;
    this.selectedRow = index;
  }

  viewDetail() {
    this.modalRef = this.modalService.show(Invoice80Component, <ModalOptions>{class: 'modal-sm'});
    this.modalRef.content.invoiceNo = this.selectedTr.refNo;
    this.modalRef.content.findInvoice();
  }

  calculateTotalAmount() {
    this.totalAmount = 0;
    for (let tr of this.transactions) {
      if (null != tr.amount)
        this.totalAmount = this.totalAmount + tr.amount
    }
  }

  /**
   * Get current date for print footer
   */
  getCurrentDate(): string {
    return new Date().toLocaleDateString();
  }

}
