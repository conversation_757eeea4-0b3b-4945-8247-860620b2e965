<div class="container-fluid px-0">
  <h2 class="component-title">Complete Summary Report</h2>
  <div class="row mb-3">
    <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
      <label class="form-label d-block d-md-none">Duration</label>
      <select type="text" required #duration="ngModel" [class.is-invalid]="duration.invalid && duration.touched"
              class="form-control" id="duration" [(ngModel)]="selectedDuration.id" name="duration"
              (ngModelChange)="filterByDuration()">
        <option *ngFor="let filter of durationFilter" [value]="filter.id">{{filter.value}}</option>
      </select>
    </div>
    <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
      <label class="form-label d-block d-md-none">Start Date</label>
      <input required #startDate="ngModel" type="text" name="startDate" id="startDate"
             [(ngModel)]="sDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             class="form-control" placeholder="Enter Start Date">
    </div>
    <div class="col-12 col-sm-6 col-md-3 mb-2 mb-sm-0">
      <label class="form-label d-block d-md-none">End Date</label>
      <input required #endDate="ngModel" type="text" name="endDate" id="endDate"
             [(ngModel)]="eDate" bsDatepicker [bsConfig]="{ dateInputFormat: 'YYYY-MM-DD' }"
             class="form-control" placeholder="Enter End Date">
    </div>
    <div class="col-12 col-sm-6 col-md-3">
      <button class="btn btn-primary btn-block" (click)="searchBetweenDates()">Search</button>
    </div>
  </div>

  <!-- Table with fixed height and vertical scrolling -->
  <div class="row mt-2" id="print-income-div">
    <div class="col-12 report-table-container">
      <table class="table table-striped table-hover">
        <thead class="table-light text-center">
        <tr>
          <th scope="col" class="d-none d-md-table-cell">Date</th>
          <th scope="col">Type</th>
          <th scope="col" class="d-none d-md-table-cell">Method</th>
          <th scope="col">Other Party</th>
          <th scope="col">Reference</th>
          <th scope="col">Amount</th>
        </tr>
        </thead>
        <tbody>
        <tr *ngFor="let tr of transactions,let i = index"
            (click)="selectTr(tr,i)" (dblclick)="viewDetail()"
            [class.active]="i === selectedRow" class="text-center">
          <td class="d-none d-md-table-cell">{{tr.date | date:'short'}}</td>
          <td>{{tr.refType}}</td>
          <td class="d-none d-md-table-cell">{{tr.paymentMethod}}</td>
          <td>{{tr.thirdParty != null ? tr.thirdParty : 'N/A'}}</td>
          <td>{{tr.refNo}}</td>
          <td>{{tr.amount | number : '1.2-2'}}</td>
        </tr>
        <tr *ngIf="!transactions || transactions.length === 0">
          <td colspan="6" class="text-center">No income transactions found for the selected period</td>
        </tr>
        </tbody>
      </table>
    </div>

    <!-- Print Footer - Only visible when printing -->
    <div class="col-12 d-none d-print-block mt-4">
      <div class="row">
        <div class="col-12">
          <p class="mb-0"><strong>Total Amount:</strong> {{ totalAmount | number : '1.2-2' }} LKR</p>
          <p class="mb-0"><strong>Total Transactions:</strong> {{ transactions?.length || 0 }}</p>
          <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile view for selected transaction details -->
  <div class="d-md-none mt-3 mb-3" *ngIf="selectedRow !== undefined && transactions && transactions.length > 0 && selectedRow !== null">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">Selected Transaction Details</h5>
        <div class="row">
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Date:</p>
            <p>{{transactions[selectedRow].date | date:'short'}}</p>
          </div>
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Method:</p>
            <p>{{transactions[selectedRow].paymentMethod}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Total Amount: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ totalAmount | number : '1.2-2' }} LKR</span>
                <span class="font-weight-bold mb-0 ml-3">Transactions: </span>
                <span class="font-weight-bold mb-0 text-success">{{ transactions?.length || 0 }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-outline-secondary mr-2" printSectionId="print-income-div" ngxPrint
                  [useExistingCss]="true" printTitle="Complete Summary Report">
            <i class="fa fa-print"></i> Print
          </button>
          <button type="button" class="btn btn-danger" (click)="viewDetail()">
            <i class="fa fa-eye"></i> View Detail
          </button>
        </div>
      </div>
    </div>
  </div>

  <ngx-spinner type="ball-scale-multiple"></ngx-spinner>
</div>
