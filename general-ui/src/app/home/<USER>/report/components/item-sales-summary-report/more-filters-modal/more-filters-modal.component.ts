import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { ToastrService } from 'ngx-toastr';
import { CashDrawer } from '../../../../trade/model/cashDrawer';
import {User} from "../../../../../admin/model/user";
import {Route} from "../../../../../admin/model/route";
import {CashDrawerService} from "../../../../trade/service/cashDrawer.service";
import {UserService} from "../../../../../admin/service/user.service";
import {RouteService} from "../../../../../admin/service/route.service";

@Component({
  standalone: false,
  selector: 'app-more-filters-modal',
  templateUrl: './more-filters-modal.component.html',
  styleUrls: ['./more-filters-modal.component.css']
})
export class ItemSalesMoreFiltersModalComponent implements OnInit {
  filterForm: FormGroup;
  loading: boolean = false;

  // Selected filter values to return to parent component
  selectedCashier: CashDrawer;
  selectedCashierUser: User;
  selectedRoute: Route;
  // Date fields removed as they are already available in the main report interface

  // Data for dropdowns
  cashDrawers: CashDrawer[] = [];
  cashierUsers: User[] = [];
  routes: Route[] = [];

  constructor(
    public modalRef: BsModalRef,
    private formBuilder: FormBuilder,
    private cashDrawerService: CashDrawerService,
    private userService: UserService,
    private routeService: RouteService,
    private toastr: ToastrService
  ) {
    this.filterForm = this.formBuilder.group({
      cashDrawerNo: [null],
      cashierUserId: [null],
      routeNo: [null]
      // Date fields removed as they are already available in the main report interface
    });
  }

  ngOnInit(): void {
    this.loadCashiers();
    this.loadRoutes();
  }

  /**
   * Load all cash drawers and users with cashier role
   */
  loadCashiers(): void {
    this.loading = true;

    // Load users with cashier role
    this.loadCashierUsers();

    // Load cash drawers
    this.loadCashDrawers();
  }

  /**
   * Load all cash drawers
   */
  loadCashDrawers(): void {
    this.cashDrawerService.findAllCashDrawers().subscribe(
      (data: CashDrawer[]) => {
        this.cashDrawers = data;

        // Check if loading is complete
        if (this.cashierUsers.length > 0) {
          this.loading = false;
        }
      },
      error => {
        console.error('Error loading cash drawers:', error);
        this.toastr.error('Failed to load cash drawers', 'Error');
        this.loading = false;
      }
    );
  }

  /**
   * Load users with cashier role
   */
  loadCashierUsers(): void {
    this.userService.findUsersWithCashierRole().subscribe(
      (data: User[]) => {
        this.cashierUsers = data;

        // Check if loading is complete
        if (this.cashDrawers.length > 0) {
          this.loading = false;
        }
      },
      error => {
        console.error('Error loading cashier users:', error);
        this.toastr.error('Failed to load users with cashier role', 'Error');
        this.loading = false;
      }
    );
  }

  /**
   * Load all routes
   */
  loadRoutes(): void {
    this.loading = true;
    this.routeService.findAll().subscribe(
      (data: any) => {
        if (data && data.content) {
          this.routes = data.content;
        }
        this.loading = false;
      },
      error => {
        console.error('Error loading routes:', error);
        this.toastr.error('Failed to load routes', 'Error');
        this.loading = false;
      }
    );
  }

  /**
   * Apply filters and close modal
   */
  applyFilters(): void {
    const cashDrawerNo = this.filterForm.get('cashDrawerNo')?.value;
    const cashierUserName = this.filterForm.get('cashierUserId')?.value;
    const routeNo = this.filterForm.get('routeNo')?.value;
    // Date fields removed as they are already available in the main report interface

    // Find selected cash drawer
    if (cashDrawerNo) {
      this.selectedCashier = this.cashDrawers.find(c => c.drawerNo === cashDrawerNo) || null;
      // If cash drawer is selected, clear user selection
      this.selectedCashierUser = null;
    } else {
      this.selectedCashier = null;
    }

    // Find selected cashier user
    if (cashierUserName) {
      this.selectedCashierUser = this.cashierUsers.find(u => u.username === cashierUserName) || null;
      // If user is selected, clear cash drawer selection
      if (this.selectedCashierUser) {
        this.selectedCashier = null;
      }
    } else {
      this.selectedCashierUser = null;
    }

    // Find selected route
    if (routeNo) {
      this.selectedRoute = this.routes.find(r => r.routeNo === routeNo) || null;
    } else {
      this.selectedRoute = null;
    }

    // Date fields removed as they are already available in the main report interface

    // Close modal
    this.modalRef.hide();
  }

  /**
   * Clear all filters
   */
  clearFilters(): void {
    this.filterForm.reset();
    this.selectedCashier = null;
    this.selectedCashierUser = null;
    this.selectedRoute = null;
    // Date fields removed as they are already available in the main report interface
  }
}
