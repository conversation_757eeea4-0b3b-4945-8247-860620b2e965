import { Component, OnInit } from '@angular/core';
import { ActionReportService } from '../../service/action-report.service';
import { Action } from '../../model/action';
import { MetaData } from '../../../../core/model/metaData';
import { NotificationService } from '../../../../core/service/notification.service';
import { FormBuilder, FormGroup } from '@angular/forms';

@Component({
  standalone: false,
  selector: 'app-action-report',
  templateUrl: './action-report.component.html',
  styleUrls: ['./action-report.component.css']
})
export class ActionReportComponent implements OnInit {
  // Data
  actions: Action[] = [];
  actionTypes: MetaData[] = [];
  selectedAction: Action;
  selectedRow: number;

  // Pagination
  page: number = 1;
  pageSize: number = 10;
  collectionSize: number = 0;
  maxSize: number = 5;

  // Filters
  filterForm: FormGroup;

  // UI state
  loading: boolean = false;

  constructor(
    private actionReportService: ActionReportService,
    private notificationService: NotificationService,
    private formBuilder: FormBuilder
  ) {
    this.filterForm = this.formBuilder.group({
      type: [''],
      reference: [''],
      remark: [''],
      startDate: [null],
      endDate: [null]
    });
  }

  ngOnInit(): void {
    this.loadActionTypes();
    this.loadActions();
  }

  /**
   * Load action types for the filter dropdown
   */
  loadActionTypes(): void {
    this.actionReportService.getActionTypes().subscribe(
      (types: MetaData[]) => {
        this.actionTypes = types;
      },
      error => {
        console.error('Error loading action types:', error);
        this.notificationService.showError('Failed to load action types');
      }
    );
  }

  /**
   * Load actions with current pagination and filters
   */
  loadActions(): void {
    this.loading = true;
    const filters = this.filterForm.value;

    this.actionReportService.findWithFilters(
      filters.type,
      filters.reference,
      filters.remark,
      filters.startDate,
      filters.endDate,
      this.page - 1, // Convert to 0-based for backend
      this.pageSize
    ).subscribe(
      (response: any) => {
        this.actions = response.content;
        this.collectionSize = response.totalElements;
        this.loading = false;
      },
      error => {
        console.error('Error loading actions:', error);
        this.notificationService.showError('Failed to load actions');
        this.loading = false;
      }
    );
  }

  /**
   * Handle page change event
   * @param event Page change event
   */
  pageChanged(event: any): void {
    this.page = event.page;
    this.loadActions();
  }

  /**
   * Apply filters from the form
   */
  applyFilters(): void {
    this.page = 1; // Reset to first page
    this.loadActions();
  }

  /**
   * Reset all filters
   */
  resetFilters(): void {
    this.filterForm.reset();
    this.page = 1;
    this.loadActions();
  }

  /**
   * Select an action row
   * @param action The selected action
   * @param index The row index
   */
  selectAction(action: Action, index: number): void {
    this.selectedAction = action;
    this.selectedRow = index;
  }
}
