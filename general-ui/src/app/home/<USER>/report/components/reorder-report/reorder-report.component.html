<div class="container-fluid px-0"> <h2 class="component-title">REORDER REPORT</h2>
  <div class="row g-2 mb-3">
    <div class="col-12 col-sm-6 col-md-3 mb-2">
      <label class="form-label d-block d-md-none">Threshold Value</label>
      <div class="input-group">
        <input [(ngModel)]="threshold"
               (ngModelChange)="findReorderList()"
               autocomplete="off" type="number"
               placeholder="Threshold Val"
               class="form-control" name="threshold">
      </div>
    </div>

    <div class="col-12 col-sm-6 col-md-3 mb-2">
      <label class="form-label d-block d-md-none">Warehouse</label>
      <select class="form-control" required #selectedWh="ngModel"
              [class.is-invalid]="selectedWh.invalid && selectedWh.touched" name="selectedWh"
              [(ngModel)]="selectedWarehouse" (change)="findReorderListByWhCode()">
        <option [ngValue]="">Select Warehouse</option>
        <option *ngFor="let wh of warehouses" [ngValue]="wh">{{ wh.name }}</option>
      </select>
    </div>

    <div class="col-12 col-sm-6 col-md-3 mb-2">
      <label class="form-label d-block d-md-none">Category</label>
      <div class="input-group">
        <input [(ngModel)]="keyItemCategory"
               [typeahead]="itemCategories"
               (typeaheadLoading)="loadItemCategories()"
               (typeaheadOnSelect)="setSelectedItemCategory($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="categoryName"
               placeholder="Category"
               autocomplete="off"
               #category="ngModel"
               class="form-control" name="category">
      </div>
    </div>

    <div class="col-12 col-sm-6 col-md-3 mb-2">
      <label class="form-label d-block d-md-none">Brand</label>
      <div class="input-group">
        <input [(ngModel)]="keyBrand"
               [typeahead]="brands"
               (typeaheadLoading)="loadBrands()"
               (typeaheadOnSelect)="setSelectedBrand($event)"
               [typeaheadOptionsLimit]="10"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="Brand"
               autocomplete="off"
               id="appendedInputButtons"
               class="form-control" name="brand">
      </div>
    </div>
  </div>

  <!-- New row for additional filters -->
  <div class="row g-2 mb-3">
    <div class="col-12 col-sm-6 col-md-4 mb-2">
      <label class="form-label d-block d-md-none">Item Name</label>
      <div class="input-group">
        <input [(ngModel)]="keyItemName"
               [typeahead]="itemsSearched"
               (typeaheadLoading)="loadItemsByName()"
               (typeaheadOnSelect)="setSelectedItem($event)"
               [typeaheadOptionsLimit]="15"
               typeaheadWaitMs="1000"
               typeaheadOptionField="itemName"
               placeholder="Item Name"
               autocomplete="off"
               class="form-control" name="itemName">
      </div>
    </div>

    <div class="col-12 col-sm-6 col-md-4 mb-2">
      <label class="form-label d-block d-md-none">Barcode</label>
      <div class="input-group">
        <input [(ngModel)]="barcode"
               (keyup.enter)="searchByBarcode()"
               placeholder="Barcode"
               autocomplete="off"
               class="form-control" name="barcode">
        <div class="input-group-append">
          <button class="btn btn-outline-secondary" type="button" (click)="searchByBarcode()">
            <i class="fa fa-search"></i>
          </button>
        </div>
      </div>
    </div>

    <div class="col-12 col-sm-6 col-md-4 mb-2">
      <label class="form-label d-block d-md-none">Supplier</label>
      <div class="input-group">
        <input [(ngModel)]="keySupplier"
               [typeahead]="suppliers"
               (typeaheadLoading)="loadSuppliers()"
               (typeaheadOnSelect)="setSelectedSupplier($event)"
               [typeaheadOptionsLimit]="10"
               typeaheadWaitMs="1000"
               typeaheadOptionField="name"
               placeholder="Supplier"
               autocomplete="off"
               class="form-control" name="supplier">
      </div>
    </div>
  </div>

  <!-- Table with fixed height and single vertical scrolling -->
  <div class="row mt-2" id="print-section">
    <div class="col-12">
      <div class="table-container">
        <table class="table table-striped table-hover">
          <thead class="table-light text-center sticky-header">
          <tr>
            <th>Barcode</th>
            <th>Item Name</th>
            <th class="d-none d-md-table-cell">Item Cost</th>
            <th class="d-none d-md-table-cell">Dead Stock Level</th>
            <th>Quantity</th>
          </tr>
          </thead>
          <tbody>
          <tr *ngFor="let stock of subStocks,let i = index"
              (click)="selectedRow = i"
              [class.active]="i === selectedRow" class="text-center">
            <td>{{ stock.barcode }}</td>
            <td>{{ stock.itemName }}</td>
            <td class="d-none d-md-table-cell">{{ stock.itemCost }}</td>
            <td class="d-none d-md-table-cell">{{ stock.deadStockLevel }}</td>
            <td>{{ stock.quantity }}</td>
          </tr>
          <tr *ngIf="!subStocks || subStocks.length === 0">
            <td colspan="5" class="text-center">No items found matching the criteria</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Mobile view for selected stock details -->
  <div class="d-md-none mt-3 mb-3"
       *ngIf="selectedRow !== undefined && subStocks && subStocks.length > 0 && selectedRow !== null">
    <div class="card bg-light">
      <div class="card-body">
        <h5 class="card-title">Selected Item Details</h5>
        <div class="row">
          <div class="col-6">
            <p class="mb-1 font-weight-bold">Item Cost:</p>
            <p>{{ subStocks[selectedRow].itemCost }}</p>
          </div>

          <div class="col-6">
            <p class="mb-1 font-weight-bold">Dead Stock Level:</p>
            <p>{{ subStocks[selectedRow].deadStockLevel }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Print Footer - Only visible when printing -->
  <div class="col-12 d-none d-print-block mt-4">
    <div class="row">
      <div class="col-12">
        <p class="mb-0"><strong>Total Items:</strong> {{ subStocks?.length || 0 }}</p>
        <p class="mb-0" *ngIf="threshold"><strong>Threshold:</strong> {{ threshold }}</p>
        <p class="mb-0" *ngIf="selectedWarehouse?.name"><strong>Warehouse:</strong> {{ selectedWarehouse?.name }}</p>
        <p class="mb-0" *ngIf="keyItemCategory"><strong>Category:</strong> {{ keyItemCategory }}</p>
        <p class="mb-0" *ngIf="keyBrand"><strong>Brand:</strong> {{ keyBrand }}</p>
        <p class="mb-0" *ngIf="keySupplier"><strong>Supplier:</strong> {{ keySupplier }}</p>
        <p class="text-right mt-3"><small>Printed on {{ getCurrentDate() }}</small></p>
      </div>
    </div>
  </div>

  <!-- Spacer for fixed footer - ensures enough space for all labels -->
  <div style="margin-bottom: 80px;"></div>

  <!-- Fixed Footer with Actions - Hidden in print -->
  <div class="fixed-bottom bg-white border-top py-2 d-print-none"
       style="box-shadow: 0 -2px 10px rgba(0,0,0,0.1); z-index: 1030;">
    <div class="container-fluid">
      <div class="row align-items-start">
        <!-- Summary Information -->
        <div class="col-12 col-md-6 mb-1 mb-md-0">
          <div class="d-flex flex-column">
            <div class="row">
              <div class="col-12 text-left">
                <span class="font-weight-bold mb-0">Total Items: </span>
                <span class="font-weight-bold mb-0 text-primary">{{ subStocks?.length || 0 }}</span>
                <span *ngIf="threshold" class="font-weight-bold mb-0 ml-3">Threshold: </span>
                <span *ngIf="threshold" class="font-weight-bold mb-0 text-info">{{ threshold }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="col-12 col-md-6 text-right">
          <button type="button" class="btn btn-danger" printSectionId="print-section" ngxPrint
                  [useExistingCss]="true" printTitle="Reorder Report">
            <i class="fa fa-print"></i> Print
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
<ngx-spinner type="ball-scale-multiple"></ngx-spinner>




