import {Component, OnInit} from '@angular/core';
import {NotificationService} from '../../../../core/service/notification.service';
import {Designation} from '../../model/designation';
import {DesignationService} from '../../service/designation.service';
import {NgForm} from '@angular/forms';
import {BsModalRef} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';

@Component({
  standalone: false,
  selector: 'app-designation',
  templateUrl: './designation.component.html',
  styleUrls: ['./designation.component.css']
})
export class DesignationComponent implements OnInit {
  designations: Array<Designation> = [];
  designation: Designation;
  selectedRow: number;
  setClickedRow: Function;
  collectionSize;
  page;
  pageSize;
  invalidDesignation: boolean;
  modalRef: BsModalRef;

  constructor(private designationService: DesignationService, private notificationSevice: NotificationService, private modalService: BsModalService) {

  }

  ngOnInit() {
    this.page = 1;
    this.pageSize = 10;
    this.designation = new Designation();
    this.designation.active = true;
    this.findAll();
    this.invalidDesignation = false;

  }

  save(form: NgForm) {
    this.designationService.save(this.designation).subscribe(result => {
      this.notificationSevice.showSuccess(result);
      this.ngOnInit();
      this.findAll();
      form.reset();
    });
  }

  findAll() {
    this.designationService.findAllPagination(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.designations = data.content;
      this.collectionSize = data.totalPages * 10;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }


  designationDetail(des) {
    this.designation = des;
  }


  Clear() {
    this.designation = new Designation();
  }

  chekValidDesignation() {
    this.designationService.findByDesignationName(this.designation.designationName).subscribe((res: boolean) => {
      this.invalidDesignation = res;
    });
  }


}
