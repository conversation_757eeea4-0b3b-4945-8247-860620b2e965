import {Component, OnInit} from '@angular/core';
import {Employee} from '../../model/employee';
import {EmployeeService} from '../../service/employee.service';
import {BsModalRef, BsModalService, ModalOptions} from 'ngx-bootstrap/modal';
import {EmployeeComponent} from '../employee/employee.component';
import {MetaData} from '../../../../core/model/metaData';
import {MetaDataService} from '../../../../core/service/metaData.service';

@Component({
  standalone: false,
  selector: 'app-manage-employee',
  templateUrl: './manage-employee.component.html',
  styleUrls: ['./manage-employee.component.css']
})
export class ManageEmployeeComponent implements OnInit {
  selectedRow: number;
  searchKey: string;
  setClickedRow: Function;
  employee: Employee;
  keyEmployee: string;
  tempEmployees: Array<Employee> = [];
  employees: Array<Employee> = [];
  page = 1;
  collectionSize;
  personType: MetaData;
  modalRef: BsModalRef;
  pageSize = 10;

  constructor(public employeeService: EmployeeService,
              public modalService: BsModalService,
              private  metaDataService: MetaDataService) {
  }

  ngOnInit() {
    this.page;
    this.pageSize;
    this.personType = new MetaData();
    this.employee = new Employee();
    this.findAll();
  }

  employeeDetail(selectedItem: any, index) {
    this.employee = selectedItem;
    this.selectedRow = index;
  }

  findAll() {
    this.employees.length = 0;
    this.tempEmployees.length = 0;
    this.employeeService.findAllEmployee(this.page - 1, this.pageSize).subscribe((data: any) => {
      this.tempEmployees = data.content;
      for (let emp of this.tempEmployees) {
        if (emp.reportingManagerId === null) {
          this.employees.push(emp)
        } else {
          this.employeeService.findByEmployeeId(emp.reportingManagerId).subscribe((data: Employee) => {
            emp.reportingManager = data;
            this.employees.push(emp);
          });
        }
      }
      this.collectionSize = data.totalPages * 10;
    });
  }

  pageChanged(event: any) {
    this.page = event.page;
    this.findAll();
  }

  openModal(notEditable) {
    this.modalRef = this.modalService.show(EmployeeComponent, <ModalOptions>{class: 'modal-lg'});
    this.modalRef.content.employee = this.employee;
    this.modalRef.content.keyEmployee = this.employee.reportingManager;
    this.modalRef.content.isView = notEditable;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalService.onHide.subscribe(result => {
      this.findAll();
    });
  }

  loadEmployees() {
    this.employees = [];
    this.employeeService.findByEmployeeNameLike(this.keyEmployee).subscribe((data: Array<Employee>) => {
      return this.employees = data;
    });
  }

  setSelectedEmployee(event) {
    this.employees = [];
    this.employeeService.findByEmployeeId(event.item.id).subscribe((data: Employee) => {
      this.employee = data;
      this.employees.push(this.employee);
    });
  }

}
