<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="text-dark">View Leaves</h2>
        <div class="btn-group">
          <button class="btn btn-outline-primary btn-sm" (click)="printReport()">
            <i class="fa fa-print"></i> Print
          </button>
          <button class="btn btn-outline-success btn-sm" (click)="exportToExcel()">
            <i class="fa fa-file-excel"></i> Excel
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Filters</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <!-- Employee Filter -->
            <div class="col-md-3 mb-3">
              <label class="form-label">Employee</label>
              <div class="input-group">
                <input [(ngModel)]="keyEmployee"
                       [typeahead]="employees"
                       (typeaheadLoading)="loadEmployees()"
                       (typeaheadOnSelect)="setSelectedEmployee($event)"
                       [typeaheadOptionsLimit]="15"
                       typeaheadOptionField="name"
                       placeholder="Search Employee"
                       autocomplete="off"
                       class="form-control">
                <button class="btn btn-outline-secondary" type="button">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>

            <!-- Leave Type Filter -->
            <div class="col-md-3 mb-3">
              <label class="form-label">Leave Type</label>
              <select class="form-select"
                      [(ngModel)]="selectedLeaveType"
                      (change)="onLeaveTypeChange()">
                <option [ngValue]="null">All Leave Types</option>
                <option *ngFor="let type of leaveTypes" [ngValue]="type">
                  {{ type.leaveType }}
                </option>
              </select>
            </div>

            <!-- Status Filter -->
            <div class="col-md-3 mb-3">
              <label class="form-label">Status</label>
              <select class="form-select"
                      [(ngModel)]="selectedStatus"
                      (change)="onStatusChange()">
                <option *ngFor="let status of statusOptions" [value]="status.value">
                  {{ status.label }}
                </option>
              </select>
            </div>

            <!-- Date Range -->
            <div class="col-md-3 mb-3">
              <label class="form-label">Actions</label>
              <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary btn-sm" (click)="clearFilters()">
                  <i class="fas fa-times"></i> Clear
                </button>
              </div>
            </div>
          </div>

          <div class="row">
            <!-- From Date -->
            <div class="col-md-3 mb-3">
              <label class="form-label">From Date</label>
              <input type="date"
                     class="form-control"
                     [(ngModel)]="fromDate"
                     (change)="onDateChange()">
            </div>

            <!-- To Date -->
            <div class="col-md-3 mb-3">
              <label class="form-label">To Date</label>
              <input type="date"
                     class="form-control"
                     [(ngModel)]="toDate"
                     (change)="onDateChange()">
            </div>

            <!-- Summary Info -->
            <div class="col-md-6 mb-3">
              <label class="form-label">Summary</label>
              <div class="d-flex gap-3 align-items-center">
                <span class="badge badge-info">Total: {{ collectionSize }}</span>
                <span class="badge badge-success" *ngIf="selectedStatus === 'Approve'">
                  Approved: {{ leaves.length }}
                </span>
                <span class="badge badge-danger" *ngIf="selectedStatus === 'Not Approve'">
                  Rejected: {{ leaves.length }}
                </span>
                <span class="badge badge-warning" *ngIf="selectedStatus === 'PENDING'">
                  Pending: {{ leaves.length }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Leaves Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Leave Records</h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Employee</th>
                  <th>Leave Type</th>
                  <th>From Date</th>
                  <th>To Date</th>
                  <th>Days</th>
                  <th>Reason</th>
                  <th>Covering Employee</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let leave of leaves">
                  <td>
                    <div>
                      <strong>{{ leave.employee?.name || 'N/A' }}</strong>
                      <br>
                      <small class="text-muted">EPF: {{ leave.employee?.epfNo || leave.epf || 'N/A' }}</small>
                    </div>
                  </td>
                  <td>
                    <span class="badge badge-info">
                      {{ leave.type?.leaveType || 'N/A' }}
                    </span>
                  </td>
                  <td>{{ formatDate(leave.from) }}</td>
                  <td>{{ formatDate(leave.to) }}</td>
                  <td>
                    <span class="badge badge-secondary">
                      {{ calculateDays(leave) }} days
                    </span>
                  </td>
                  <td>
                    <span [title]="leave.reason" class="text-truncate d-inline-block" style="max-width: 150px;">
                      {{ leave.reason || 'N/A' }}
                    </span>
                  </td>
                  <td>
                    <div *ngIf="leave.coveringEmp">
                      <strong>{{ leave.coveringEmp.name }}</strong>
                      <br>
                      <small class="text-muted">{{ leave.coveringEmp.epfNo }}</small>
                    </div>
                    <span *ngIf="!leave.coveringEmp" class="text-muted">N/A</span>
                  </td>
                  <td>
                    <span class="badge" [class]="getStatusClass(leave)">
                      {{ getStatusText(leave) }}
                    </span>
                  </td>
                </tr>
                <tr *ngIf="leaves.length === 0">
                  <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No leave records found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="row mt-4">
    <div class="col-md-3">
      <div class="card bg-info text-white">
        <div class="card-body text-center">
          <h6>Total Leaves</h6>
          <h4>{{ collectionSize }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-success text-white">
        <div class="card-body text-center">
          <h6>Approved</h6>
          <h4>{{ getApprovedCount() }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-danger text-white">
        <div class="card-body text-center">
          <h6>Rejected</h6>
          <h4>{{ getRejectedCount() }}</h4>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card bg-warning text-white">
        <div class="card-body text-center">
          <h6>Pending</h6>
          <h4>{{ getPendingCount() }}</h4>
        </div>
      </div>
    </div>
  </div>
</div>
