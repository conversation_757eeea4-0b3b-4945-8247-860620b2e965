import { Component, OnInit } from '@angular/core';
import { LeaveRequestService } from '../../service/leave-request.service';
import { EmployeeService } from '../../service/employee.service';
import { LeaveService } from '../../service/leave.service';
import { LeaveRequest } from '../../model/leave-request';
import { Employee } from '../../model/employee';
import { Leave } from '../../model/leave';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-view-leaves',
  templateUrl: './view-leaves.component.html',
  styleUrls: ['./view-leaves.component.css']
})
export class ViewLeavesComponent implements OnInit {

  leaves: Array<LeaveRequest> = [];
  employees: Array<Employee> = [];
  leaveTypes: Array<Leave> = [];

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Filters
  selectedEmployee: Employee = null;
  selectedLeaveType: Leave = null;
  selectedStatus = 'ALL';
  fromDate: Date = null;
  toDate: Date = null;

  // Search
  keyEmployee: string = '';

  // Status options
  statusOptions = [
    { value: 'ALL', label: 'All Leaves' },
    { value: 'Approve', label: 'Approved' },
    { value: 'Not Approve', label: 'Rejected' },
    { value: 'PENDING', label: 'Pending' }
  ];

  constructor(
    private leaveRequestService: LeaveRequestService,
    private employeeService: EmployeeService,
    private leaveService: LeaveService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.loadLeaves();
    this.loadLeaveTypes();
  }

  loadLeaves(): void {
    this.leaveRequestService.findAllLeaves().subscribe(
      (data: Array<LeaveRequest>) => {
        this.leaves = data || [];
        this.collectionSize = this.leaves.length;
        this.applyFilters();
      },
      (error) => {
        console.error('Error loading leaves:', error);
        this.leaves = [];
      }
    );
  }

  loadEmployees(): void {
    if (this.keyEmployee && this.keyEmployee.length > 2) {
      this.employeeService.findByEmployeeNameLike(this.keyEmployee).subscribe(
        (data: Array<Employee>) => {
          this.employees = data || [];
        },
        (error) => {
          console.error('Error loading employees:', error);
          this.employees = [];
        }
      );
    }
  }

  loadLeaveTypes(): void {
    this.leaveService.getAll().subscribe(
      (data: Array<Leave>) => {
        this.leaveTypes = data || [];
      },
      (error) => {
        console.error('Error loading leave types:', error);
        this.leaveTypes = [];
      }
    );
  }

  setSelectedEmployee(event: any): void {
    if (event.item) {
      this.selectedEmployee = event.item;
      this.applyFilters();
    }
  }

  onLeaveTypeChange(): void {
    this.applyFilters();
  }

  onStatusChange(): void {
    this.applyFilters();
  }

  onDateChange(): void {
    this.applyFilters();
  }

  applyFilters(): void {
    let filteredLeaves = [...this.leaves];

    // Filter by employee
    if (this.selectedEmployee) {
      filteredLeaves = filteredLeaves.filter(leave =>
        leave.employee && leave.employee.id === this.selectedEmployee.id
      );
    }

    // Filter by leave type
    if (this.selectedLeaveType) {
      filteredLeaves = filteredLeaves.filter(leave =>
        leave.type && leave.type.id === this.selectedLeaveType.id
      );
    }

    // Filter by status
    if (this.selectedStatus !== 'ALL') {
      if (this.selectedStatus === 'PENDING') {
        filteredLeaves = filteredLeaves.filter(leave => !leave.leaveStatus);
      } else {
        filteredLeaves = filteredLeaves.filter(leave =>
          leave.leaveStatus && leave.leaveStatus.value === this.selectedStatus
        );
      }
    }

    // Filter by date range
    if (this.fromDate) {
      filteredLeaves = filteredLeaves.filter(leave =>
        new Date(leave.from) >= new Date(this.fromDate)
      );
    }

    if (this.toDate) {
      filteredLeaves = filteredLeaves.filter(leave =>
        new Date(leave.to) <= new Date(this.toDate)
      );
    }

    this.leaves = filteredLeaves;
    this.collectionSize = this.leaves.length;
  }

  clearFilters(): void {
    this.selectedEmployee = null;
    this.selectedLeaveType = null;
    this.selectedStatus = 'ALL';
    this.fromDate = null;
    this.toDate = null;
    this.keyEmployee = '';
    this.loadLeaves();
  }

  formatDate(date: any): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }

  getStatusClass(leave: LeaveRequest): string {
    if (!leave.leaveStatus) {
      return 'badge-warning'; // Pending
    }

    switch (leave.leaveStatus.value) {
      case 'Approve': return 'badge-success';
      case 'Not Approve': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  getStatusText(leave: LeaveRequest): string {
    if (!leave.leaveStatus) {
      return 'Pending';
    }

    switch (leave.leaveStatus.value) {
      case 'Approve': return 'Approved';
      case 'Not Approve': return 'Rejected';
      default: return leave.leaveStatus.value;
    }
  }

  calculateDays(leave: LeaveRequest): number {
    if (leave.from && leave.to) {
      const fromDate = new Date(leave.from);
      const toDate = new Date(leave.to);
      const timeDiff = toDate.getTime() - fromDate.getTime();
      return Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates
    }
    return 0;
  }

  exportToExcel(): void {
    // TODO: Implement Excel export functionality
    this.notificationService.showInfo('Excel export functionality will be implemented');
  }

  printReport(): void {
    window.print();
  }

  getApprovedCount(): number {
    return this.leaves.filter(leave => leave.leaveStatus && leave.leaveStatus.value === 'Approve').length;
  }

  getRejectedCount(): number {
    return this.leaves.filter(leave => leave.leaveStatus && leave.leaveStatus.value === 'Not Approve').length;
  }

  getPendingCount(): number {
    return this.leaves.filter(leave => !leave.leaveStatus).length;
  }
}
