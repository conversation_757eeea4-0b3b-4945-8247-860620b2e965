<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Salary Advance Management</h2>
    </div>
  </div>

  <div class="row">
    <!-- Create Salary Advance Form -->
    <div class="col-lg-5">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Request Salary Advance</h5>
        </div>
        <div class="card-body">
          <form #salaryAdvanceForm="ngForm">
            <!-- Employee Selection -->
            <div class="mb-3">
              <label class="form-label">Employee *</label>
              <div class="input-group">
                <input [(ngModel)]="keyEmployee"
                       [typeahead]="employees"
                       (typeaheadLoading)="loadEmployees()"
                       (typeaheadOnSelect)="setSelectedEmployee($event)"
                       [typeaheadOptionsLimit]="15"
                       typeaheadOptionField="name"
                       placeholder="Search Employee"
                       autocomplete="off"
                       class="form-control"
                       name="employee"
                       required>
                <button class="btn btn-outline-secondary" type="button">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>

            <!-- Employee Salary Information -->
            <div class="mb-3" *ngIf="salaryAdvance.employee && salaryAdvance.employee.salaryScale">
              <div class="alert alert-info">
                <strong>Employee Salary:</strong> {{ formatCurrency(salaryAdvance.employee.salaryScale.basicSalary) }}
                <br>
                <small>Maximum advance amount allowed</small>
              </div>
            </div>

            <!-- Advance Amount -->
            <div class="mb-3">
              <label class="form-label">Advance Amount *</label>
              <input type="number"
                     class="form-control"
                     name="advanceAmount"
                     [(ngModel)]="salaryAdvance.advanceAmount"
                     required
                     min="1"
                     [max]="salaryAdvance.employee?.salaryScale?.basicSalary || 999999"
                     step="0.01"
                     placeholder="Enter advance amount">
              <small class="form-text text-muted" *ngIf="salaryAdvance.employee?.salaryScale?.basicSalary">
                Maximum allowed: {{ formatCurrency(salaryAdvance.employee.salaryScale.basicSalary) }}
              </small>
            </div>

            <!-- Request Date -->
            <div class="mb-3">
              <label class="form-label">Request Date</label>
              <input type="date"
                     class="form-control"
                     name="requestDate"
                     [(ngModel)]="salaryAdvance.requestDate"
                     required>
            </div>

            <!-- Reason -->
            <div class="mb-3">
              <label class="form-label">Reason *</label>
              <textarea class="form-control"
                        name="reason"
                        [(ngModel)]="salaryAdvance.reason"
                        required
                        rows="3"
                        placeholder="Enter reason for advance"></textarea>
            </div>

            <!-- Note about advance payment -->
            <div class="mb-3">
              <div class="alert alert-warning">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Note:</strong> Salary advance will be deducted from the next salary payment in full.
              </div>
            </div>

            <!-- Buttons -->
            <div class="d-flex gap-2 justify-content-end">
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
              <button type="submit"
                      class="btn btn-primary"
                      [disabled]="!salaryAdvanceForm.valid"
                      (click)="save(salaryAdvanceForm)">
                <i class="fas fa-save me-1"></i>Save Request
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Salary Advances List -->
    <div class="col-lg-7">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Salary Advances</h5>
            <div class="d-flex gap-2">
              <!-- Status Filter -->
              <select class="form-select form-select-sm"
                      [(ngModel)]="statusFilter"
                      (change)="filterByStatus()"
                      style="width: auto;">
                <option *ngFor="let status of statusOptions" [value]="status.value">
                  {{ status.label }}
                </option>
              </select>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Employee</th>
                  <th>Amount</th>
                  <th>Request Date</th>
                  <th>Status</th>
                  <th>Reason</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let advance of salaryAdvances; let i = index"
                    [class.table-active]="selectedSalaryAdvance?.id === advance.id"
                    (click)="selectSalaryAdvance(advance, i)"
                    style="cursor: pointer;">
                  <td>
                    <div>
                      <strong>{{ advance.employee?.name || 'N/A' }}</strong>
                      <br>
                      <small class="text-muted">EPF: {{ advance.employee?.epfNo || 'N/A' }}</small>
                    </div>
                  </td>
                  <td>{{ formatCurrency(advance.advanceAmount) }}</td>
                  <td>{{ formatDate(advance.requestDate) }}</td>
                  <td>
                    <span class="badge" [class]="getStatusClass(advance.status)">
                      {{ advance.status }}
                    </span>
                  </td>
                  <td>
                    <span [title]="advance.reason" class="text-truncate d-inline-block" style="max-width: 150px;">
                      {{ advance.reason || 'N/A' }}
                    </span>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-success btn-sm"
                              *ngIf="canApprove(advance)"
                              (click)="approveAdvance(); $event.stopPropagation()"
                              title="Approve">
                        <i class="fas fa-check"></i>
                      </button>
                      <button class="btn btn-outline-danger btn-sm"
                              *ngIf="canApprove(advance)"
                              (click)="rejectAdvance(); $event.stopPropagation()"
                              title="Reject">
                        <i class="fas fa-times"></i>
                      </button>
                      <button class="btn btn-outline-primary btn-sm"
                              *ngIf="canMarkAsPaid(advance)"
                              (click)="markAsPaid(); $event.stopPropagation()"
                              title="Mark as Paid">
                        <i class="fas fa-money-bill"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="salaryAdvances.length === 0">
                  <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No salary advances found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination -->
        <div class="card-footer" *ngIf="collectionSize > pageSize">
          <ngb-pagination
            [(page)]="page"
            [pageSize]="pageSize"
            [collectionSize]="collectionSize"
            (pageChange)="pageChanged($event)"
            [maxSize]="5"
            [rotate]="true"
            class="d-flex justify-content-center">
          </ngb-pagination>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Advance Details -->
  <div class="row mt-4" *ngIf="selectedSalaryAdvance">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Advance Details</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Employee:</strong></td>
                  <td>{{ selectedSalaryAdvance.employee?.name }}</td>
                </tr>
                <tr>
                  <td><strong>EPF No:</strong></td>
                  <td>{{ selectedSalaryAdvance.employee?.epfNo }}</td>
                </tr>
                <tr>
                  <td><strong>Advance Amount:</strong></td>
                  <td>{{ formatCurrency(selectedSalaryAdvance.advanceAmount) }}</td>
                </tr>
                <tr>
                  <td><strong>Request Date:</strong></td>
                  <td>{{ formatDate(selectedSalaryAdvance.requestDate) }}</td>
                </tr>
                <tr>
                  <td><strong>Status:</strong></td>
                  <td>
                    <span class="badge" [class]="getStatusClass(selectedSalaryAdvance.status)">
                      {{ selectedSalaryAdvance.status }}
                    </span>
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <table class="table table-borderless table-sm">
                <tr *ngIf="selectedSalaryAdvance.approvedDate">
                  <td><strong>Approved Date:</strong></td>
                  <td>{{ formatDate(selectedSalaryAdvance.approvedDate) }}</td>
                </tr>
                <tr *ngIf="selectedSalaryAdvance.approvedBy">
                  <td><strong>Approved By:</strong></td>
                  <td>{{ selectedSalaryAdvance.approvedBy }}</td>
                </tr>
                <tr *ngIf="selectedSalaryAdvance.paidDate">
                  <td><strong>Paid Date:</strong></td>
                  <td>{{ formatDate(selectedSalaryAdvance.paidDate) }}</td>
                </tr>
                <tr *ngIf="selectedSalaryAdvance.numberOfInstallments">
                  <td><strong>Installments:</strong></td>
                  <td>{{ selectedSalaryAdvance.numberOfInstallments }} × {{ formatCurrency(selectedSalaryAdvance.installmentAmount) }}</td>
                </tr>
                <tr *ngIf="selectedSalaryAdvance.remainingAmount">
                  <td><strong>Remaining Amount:</strong></td>
                  <td>{{ formatCurrency(selectedSalaryAdvance.remainingAmount) }}</td>
                </tr>
              </table>
            </div>
          </div>
          <div class="row" *ngIf="selectedSalaryAdvance.reason">
            <div class="col-12">
              <strong>Reason:</strong>
              <p class="mt-2">{{ selectedSalaryAdvance.reason }}</p>
            </div>
          </div>
          <div class="row" *ngIf="selectedSalaryAdvance.remarks">
            <div class="col-12">
              <strong>Remarks:</strong>
              <p class="mt-2">{{ selectedSalaryAdvance.remarks }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
