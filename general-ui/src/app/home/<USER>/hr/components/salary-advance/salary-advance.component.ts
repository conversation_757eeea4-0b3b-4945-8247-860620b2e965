import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { SalaryAdvance } from '../../model/salary-advance';
import { Employee } from '../../model/employee';
import { SalaryAdvanceService } from '../../service/salary-advance.service';
import { EmployeeService } from '../../service/employee.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-salary-advance',
  templateUrl: './salary-advance.component.html',
  styleUrls: ['./salary-advance.component.css']
})
export class SalaryAdvanceComponent implements OnInit {

  salaryAdvance = new SalaryAdvance();
  employees: Array<Employee> = [];
  salaryAdvances: Array<SalaryAdvance> = [];
  selectedSalaryAdvance: SalaryAdvance = null;

  // Pagination
  page = 1;
  pageSize = 10;
  collectionSize = 0;

  // Search
  keyEmployee: string = '';

  // Status filter
  statusFilter = 'ALL';
  statusOptions = [
    { value: 'ALL', label: 'All Advances' },
    { value: 'PENDING', label: 'Pending Approval' },
    { value: 'APPROVED', label: 'Approved' },
    { value: 'PAID', label: 'Paid' },
    { value: 'REJECTED', label: 'Rejected' }
  ];

  constructor(
    private salaryAdvanceService: SalaryAdvanceService,
    private employeeService: EmployeeService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.loadSalaryAdvances();
  }

  initializeForm(): void {
    this.salaryAdvance = new SalaryAdvance();
    this.salaryAdvance.employee = new Employee();
    this.salaryAdvance.requestDate = new Date();
    this.salaryAdvance.status = 'PENDING';
    this.salaryAdvance.active = true;
    this.salaryAdvance.numberOfInstallments = 1;
  }

  save(form: NgForm): void {
    if (!this.salaryAdvance.employee || !this.salaryAdvance.employee.id) {
      this.notificationService.showError('Please select an employee');
      return;
    }

    if (!this.salaryAdvance.advanceAmount || this.salaryAdvance.advanceAmount <= 0) {
      this.notificationService.showError('Please enter a valid advance amount');
      return;
    }

    // Validate advance amount doesn't exceed employee's salary
    const employeeSalary = this.salaryAdvance.employee.salaryScale?.basicSalary || 0;
    if (this.salaryAdvance.advanceAmount > employeeSalary) {
      this.notificationService.showError(`Advance amount cannot exceed employee's basic salary (${employeeSalary.toLocaleString()})`);
      return;
    }

    // Convert month string to proper date if needed
    if (this.salaryAdvance.deductionStartMonth) {
      const monthStr = this.salaryAdvance.deductionStartMonth.toString();
      if (monthStr.length === 7) { // Format: "2025-07"
        this.salaryAdvance.deductionStartMonth = new Date(monthStr + '-01'); // Convert to "2025-07-01"
      }
    }

    // Remove installment logic as per requirement
    this.salaryAdvance.numberOfInstallments = 1;
    this.salaryAdvance.installmentAmount = this.salaryAdvance.advanceAmount;
    this.salaryAdvance.remainingAmount = this.salaryAdvance.advanceAmount;

    this.salaryAdvanceService.save(this.salaryAdvance).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Salary advance saved successfully', 'Failed to save salary advance');
        form.reset();
        this.initializeForm();
        this.loadSalaryAdvances();
      },
      (error) => {
        this.notificationService.showError('Failed to save salary advance: ' + (error.message || 'Unknown error'));
      }
    );
  }

  loadSalaryAdvances(): void {
    this.salaryAdvanceService.findAll(this.page - 1, this.pageSize).subscribe(
      (data: any) => {
        this.salaryAdvances = data.content || data;
        this.collectionSize = data.totalElements || this.salaryAdvances.length;
      },
      (error) => {
        console.error('Error loading salary advances:', error);
        this.salaryAdvances = [];
      }
    );
  }

  loadEmployees(): void {
    if (this.keyEmployee && this.keyEmployee.length > 2) {
      this.employeeService.findByEmployeeNameLike(this.keyEmployee).subscribe(
        (data: Array<Employee>) => {
          this.employees = data || [];
        },
        (error) => {
          console.error('Error loading employees:', error);
          this.employees = [];
        }
      );
    }
  }

  setSelectedEmployee(event: any): void {
    if (event.item) {
      this.salaryAdvance.employee = event.item;

      // Check if employee has salary scale
      if (!event.item.salaryScale || !event.item.salaryScale.basicSalary) {
        this.notificationService.showWarning('Selected employee does not have a salary scale assigned. Please assign a salary scale first.');
      }
    }
  }

  selectSalaryAdvance(advance: SalaryAdvance, index: number): void {
    this.selectedSalaryAdvance = advance;
  }

  approveAdvance(): void {
    if (!this.selectedSalaryAdvance) {
      this.notificationService.showError('Please select a salary advance to approve');
      return;
    }

    this.salaryAdvanceService.approve(this.selectedSalaryAdvance.id, 'Admin').subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Salary advance approved successfully', 'Failed to approve salary advance');
        this.loadSalaryAdvances();
        this.selectedSalaryAdvance = null;
      },
      (error) => {
        this.notificationService.showError('Failed to approve salary advance: ' + (error.message || 'Unknown error'));
      }
    );
  }

  rejectAdvance(): void {
    if (!this.selectedSalaryAdvance) {
      this.notificationService.showError('Please select a salary advance to reject');
      return;
    }

    const remarks = prompt('Enter rejection reason:');
    if (remarks) {
      this.salaryAdvanceService.reject(this.selectedSalaryAdvance.id, remarks).subscribe(
        (result) => {
          this.notificationService.handleResponse(result, 'Salary advance rejected successfully', 'Failed to reject salary advance');
          this.loadSalaryAdvances();
          this.selectedSalaryAdvance = null;
        },
        (error) => {
          this.notificationService.showError('Failed to reject salary advance: ' + (error.message || 'Unknown error'));
        }
      );
    }
  }

  markAsPaid(): void {
    if (!this.selectedSalaryAdvance) {
      this.notificationService.showError('Please select a salary advance to mark as paid');
      return;
    }

    this.salaryAdvanceService.markAsPaid(this.selectedSalaryAdvance.id).subscribe(
      (result) => {
        this.notificationService.handleResponse(result, 'Salary advance marked as paid successfully', 'Failed to mark as paid');
        this.loadSalaryAdvances();
        this.selectedSalaryAdvance = null;
      },
      (error) => {
        this.notificationService.showError('Failed to mark as paid: ' + (error.message || 'Unknown error'));
      }
    );
  }

  filterByStatus(): void {
    if (this.statusFilter === 'ALL') {
      this.loadSalaryAdvances();
    } else {
      this.salaryAdvanceService.findByStatus(this.statusFilter).subscribe(
        (data: Array<SalaryAdvance>) => {
          this.salaryAdvances = data || [];
          this.collectionSize = this.salaryAdvances.length;
        },
        (error) => {
          console.error('Error filtering by status:', error);
          this.salaryAdvances = [];
        }
      );
    }
  }

  pageChanged(event: any): void {
    this.page = event.page;
    this.loadSalaryAdvances();
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }

  formatDate(date: any): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING': return 'badge-warning';
      case 'APPROVED': return 'badge-success';
      case 'PAID': return 'badge-primary';
      case 'REJECTED': return 'badge-danger';
      default: return 'badge-secondary';
    }
  }

  canApprove(advance: SalaryAdvance): boolean {
    return advance && advance.status === 'PENDING';
  }

  canMarkAsPaid(advance: SalaryAdvance): boolean {
    return advance && advance.status === 'APPROVED';
  }

  clear(): void {
    this.initializeForm();
  }
}
