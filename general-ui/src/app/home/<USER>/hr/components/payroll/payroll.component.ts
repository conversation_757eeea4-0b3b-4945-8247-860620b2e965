import { Component, OnInit } from '@angular/core';
import { NgForm } from '@angular/forms';
import { PayrollRecord } from '../../model/payroll';
import { Employee } from '../../model/employee';
import { SalaryAdvance } from '../../model/salary-advance';
import { EmployeeService } from '../../service/employee.service';
import { SalaryAdvanceService } from '../../service/salary-advance.service';
import { NotificationService } from '../../../../core/service/notification.service';

@Component({
  standalone: false,
  selector: 'app-payroll',
  templateUrl: './payroll.component.html',
  styleUrls: ['./payroll.component.css']
})
export class PayrollComponent implements OnInit {

  payrollRecord = new PayrollRecord();
  employees: Array<Employee> = [];
  payrollRecords: Array<PayrollRecord> = [];
  selectedPayrollRecord: PayrollRecord = null;

  // Search
  keyEmployee: string = '';

  // Payroll processing
  selectedEmployee: Employee = null;
  payPeriod: Date = new Date();

  // Tax and contribution rates (configurable)
  epfEmployeeRate = 8; // 8%
  epfEmployerRate = 12; // 12%
  etfEmployerRate = 3; // 3%
  incomeTaxRate = 10; // 10% (simplified)

  constructor(
    private employeeService: EmployeeService,
    private salaryAdvanceService: SalaryAdvanceService,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.initializeForm();
    this.loadPayrollRecords();
  }

  initializeForm(): void {
    this.payrollRecord = new PayrollRecord();
    this.selectedEmployee = null;
    this.keyEmployee = '';
    this.payPeriod = new Date();
  }

  loadEmployees(): void {
    if (this.keyEmployee && this.keyEmployee.length > 2) {
      this.employeeService.findByEmployeeNameLike(this.keyEmployee).subscribe(
        (data: Array<Employee>) => {
          this.employees = data || [];
        },
        (error) => {
          console.error('Error loading employees:', error);
          this.employees = [];
        }
      );
    }
  }

  setSelectedEmployee(event: any): void {
    if (event.item) {
      this.selectedEmployee = event.item;
      this.payrollRecord.employee = event.item;
      this.loadEmployeeSalaryData();
    }
  }

  loadEmployeeSalaryData(): void {
    if (this.selectedEmployee && this.selectedEmployee.salaryScale) {
      // Set basic salary from salary scale
      this.payrollRecord.basicSalary = this.selectedEmployee.salaryScale.basicSalary || 0;

      // Load outstanding salary advances
      this.salaryAdvanceService.getAdvancesByEmployee(this.selectedEmployee.id).subscribe(
        (advances: Array<SalaryAdvance>) => {
          this.payrollRecord.salaryAdvances = advances.filter(advance =>
            advance.status === 'APPROVED' && advance.remainingAmount > 0
          );
          this.calculateSalaryAdvanceDeduction();
        },
        (error) => {
          console.error('Error loading salary advances:', error);
          this.payrollRecord.salaryAdvances = [];
        }
      );

      this.calculateSalary();
    }
  }

  calculateSalaryAdvanceDeduction(): void {
    let totalDeduction = 0;

    // Since advances are now single payments, deduct the full remaining amount
    this.payrollRecord.salaryAdvances.forEach(advance => {
      if (advance.remainingAmount > 0) {
        totalDeduction += advance.remainingAmount;
      }
    });

    this.payrollRecord.salaryAdvanceDeduction = totalDeduction;
    this.calculateSalary();
  }

  calculateSalary(): void {
    // Calculate gross salary
    this.payrollRecord.grossSalary =
      (this.payrollRecord.basicSalary || 0) +
      (this.payrollRecord.allowances || 0) +
      (this.payrollRecord.overtime || 0);

    // Calculate EPF employee contribution
    this.payrollRecord.epfEmployee = this.payrollRecord.basicSalary * (this.epfEmployeeRate / 100);

    // Calculate EPF employer contribution
    this.payrollRecord.epfEmployer = this.payrollRecord.basicSalary * (this.epfEmployerRate / 100);

    // Calculate ETF employer contribution
    this.payrollRecord.etfEmployer = this.payrollRecord.basicSalary * (this.etfEmployerRate / 100);

    // Calculate income tax (simplified calculation)
    if (this.payrollRecord.grossSalary > 50000) {
      this.payrollRecord.incomeTax = (this.payrollRecord.grossSalary - 50000) * (this.incomeTaxRate / 100);
    } else {
      this.payrollRecord.incomeTax = 0;
    }

    // Calculate total deductions
    this.payrollRecord.totalDeductions =
      (this.payrollRecord.epfEmployee || 0) +
      (this.payrollRecord.incomeTax || 0) +
      (this.payrollRecord.salaryAdvanceDeduction || 0) +
      (this.payrollRecord.otherDeductions || 0);

    // Calculate net salary
    this.payrollRecord.netSalary = this.payrollRecord.grossSalary - this.payrollRecord.totalDeductions;
  }

  onSalaryFieldChange(): void {
    this.calculateSalary();
  }

  processPayroll(form: NgForm): void {
    if (!this.selectedEmployee) {
      this.notificationService.showError('Please select an employee');
      return;
    }

    if (!this.payrollRecord.basicSalary || this.payrollRecord.basicSalary <= 0) {
      this.notificationService.showError('Please enter a valid basic salary');
      return;
    }

    this.payrollRecord.payPeriod = this.payPeriod;
    this.payrollRecord.processedDate = new Date();
    this.payrollRecord.status = 'PROCESSED';

    // TODO: Save payroll record to backend
    this.notificationService.showSuccess('Payroll processed successfully');

    // Add to local records for display
    this.payrollRecords.unshift({...this.payrollRecord});

    // Reset form
    this.initializeForm();
    form.reset();
  }

  loadPayrollRecords(): void {
    // TODO: Load from backend service
    this.payrollRecords = [];
  }

  selectPayrollRecord(record: PayrollRecord, index: number): void {
    this.selectedPayrollRecord = record;
  }

  printSalarySlip(): void {
    if (!this.selectedPayrollRecord) {
      this.notificationService.showError('Please select a payroll record to print');
      return;
    }

    // Open salary slip in new window for printing
    this.openSalarySlipWindow();
  }

  openSalarySlipWindow(): void {
    const record = this.selectedPayrollRecord;
    const salarySlipContent = this.generateSalarySlipHTML(record);

    const printWindow = window.open('', '_blank', 'width=800,height=600');
    printWindow.document.write(salarySlipContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  }

  generateSalarySlipHTML(record: PayrollRecord): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Salary Slip</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 20px; }
          .employee-info { margin-bottom: 20px; }
          .salary-table { width: 100%; border-collapse: collapse; }
          .salary-table th, .salary-table td { border: 1px solid #000; padding: 8px; text-align: left; }
          .salary-table th { background-color: #f0f0f0; }
          .total-row { font-weight: bold; background-color: #f9f9f9; }
          .text-right { text-align: right; }
        </style>
      </head>
      <body>
        <div class="header">
          <h2>SALARY SLIP</h2>
          <p>Pay Period: ${this.formatDate(record.payPeriod)}</p>
        </div>

        <div class="employee-info">
          <table style="width: 100%;">
            <tr>
              <td><strong>Employee Name:</strong> ${record.employee?.name || 'N/A'}</td>
              <td><strong>EPF No:</strong> ${record.employee?.epfNo || 'N/A'}</td>
            </tr>
            <tr>
              <td><strong>Department:</strong> ${record.employee?.department?.departmentName || 'N/A'}</td>
              <td><strong>Designation:</strong> ${record.employee?.designation?.designationName || 'N/A'}</td>
            </tr>
          </table>
        </div>

        <table class="salary-table">
          <thead>
            <tr>
              <th>Description</th>
              <th class="text-right">Amount (LKR)</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Basic Salary</td>
              <td class="text-right">${this.formatCurrency(record.basicSalary)}</td>
            </tr>
            <tr>
              <td>Allowances</td>
              <td class="text-right">${this.formatCurrency(record.allowances)}</td>
            </tr>
            <tr>
              <td>Overtime</td>
              <td class="text-right">${this.formatCurrency(record.overtime)}</td>
            </tr>
            <tr class="total-row">
              <td><strong>Gross Salary</strong></td>
              <td class="text-right"><strong>${this.formatCurrency(record.grossSalary)}</strong></td>
            </tr>
            <tr>
              <td colspan="2"><strong>DEDUCTIONS</strong></td>
            </tr>
            <tr>
              <td>EPF Employee (${this.epfEmployeeRate}%)</td>
              <td class="text-right">${this.formatCurrency(record.epfEmployee)}</td>
            </tr>
            <tr>
              <td>Income Tax</td>
              <td class="text-right">${this.formatCurrency(record.incomeTax)}</td>
            </tr>
            <tr>
              <td>Salary Advance Deduction</td>
              <td class="text-right">${this.formatCurrency(record.salaryAdvanceDeduction)}</td>
            </tr>
            <tr>
              <td>Other Deductions</td>
              <td class="text-right">${this.formatCurrency(record.otherDeductions)}</td>
            </tr>
            <tr class="total-row">
              <td><strong>Total Deductions</strong></td>
              <td class="text-right"><strong>${this.formatCurrency(record.totalDeductions)}</strong></td>
            </tr>
            <tr class="total-row" style="background-color: #e8f5e8;">
              <td><strong>NET SALARY</strong></td>
              <td class="text-right"><strong>${this.formatCurrency(record.netSalary)}</strong></td>
            </tr>
          </tbody>
        </table>

        <div style="margin-top: 30px;">
          <p><strong>Employer Contributions:</strong></p>
          <p>EPF Employer (${this.epfEmployerRate}%): LKR ${this.formatCurrency(record.epfEmployer)}</p>
          <p>ETF Employer (${this.etfEmployerRate}%): LKR ${this.formatCurrency(record.etfEmployer)}</p>
        </div>

        <div style="margin-top: 30px; text-align: center;">
          <p>This is a computer-generated salary slip.</p>
          <p>Generated on: ${new Date().toLocaleDateString()}</p>
        </div>
      </body>
      </html>
    `;
  }

  formatCurrency(amount: number): string {
    return amount ? amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) : '0.00';
  }

  formatDate(date: any): string {
    return date ? new Date(date).toLocaleDateString() : '';
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'DRAFT': return 'badge-secondary';
      case 'PROCESSED': return 'badge-success';
      case 'PAID': return 'badge-primary';
      default: return 'badge-secondary';
    }
  }

  clear(): void {
    this.initializeForm();
  }

  markAsPaid(): void {
    if (!this.selectedPayrollRecord) {
      this.notificationService.showError('Please select a payroll record to mark as paid');
      return;
    }

    this.selectedPayrollRecord.status = 'PAID';
    this.selectedPayrollRecord.paidDate = new Date();
    this.notificationService.showSuccess('Payroll marked as paid successfully');
  }
}
