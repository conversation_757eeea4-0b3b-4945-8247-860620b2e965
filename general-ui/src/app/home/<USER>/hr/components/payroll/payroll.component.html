<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <h2 class="text-dark mb-4">Payroll Management</h2>
    </div>
  </div>

  <div class="row">
    <!-- Payroll Processing Form -->
    <div class="col-lg-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Process Payroll</h5>
        </div>
        <div class="card-body">
          <form #payrollForm="ngForm">
            <!-- Employee Selection -->
            <div class="mb-3">
              <label class="form-label">Employee *</label>
              <div class="input-group">
                <input [(ngModel)]="keyEmployee"
                       [typeahead]="employees"
                       (typeaheadLoading)="loadEmployees()"
                       (typeaheadOnSelect)="setSelectedEmployee($event)"
                       [typeaheadOptionsLimit]="15"
                       typeaheadOptionField="name"
                       placeholder="Search Employee"
                       autocomplete="off"
                       class="form-control"
                       name="employee"
                       required>
                <button class="btn btn-outline-secondary" type="button">
                  <i class="fas fa-search"></i>
                </button>
              </div>
            </div>

            <!-- Pay Period -->
            <div class="mb-3">
              <label class="form-label">Pay Period *</label>
              <input type="month"
                     class="form-control"
                     name="payPeriod"
                     [(ngModel)]="payPeriod"
                     required>
            </div>

            <!-- Basic Salary -->
            <div class="mb-3">
              <label class="form-label">Basic Salary *</label>
              <input type="number"
                     class="form-control"
                     name="basicSalary"
                     [(ngModel)]="payrollRecord.basicSalary"
                     (ngModelChange)="onSalaryFieldChange()"
                     required
                     min="0"
                     step="0.01"
                     placeholder="Enter basic salary">
            </div>

            <!-- Allowances -->
            <div class="mb-3">
              <label class="form-label">Allowances</label>
              <input type="number"
                     class="form-control"
                     name="allowances"
                     [(ngModel)]="payrollRecord.allowances"
                     (ngModelChange)="onSalaryFieldChange()"
                     min="0"
                     step="0.01"
                     placeholder="Enter allowances">
            </div>

            <!-- Overtime -->
            <div class="mb-3">
              <label class="form-label">Overtime</label>
              <input type="number"
                     class="form-control"
                     name="overtime"
                     [(ngModel)]="payrollRecord.overtime"
                     (ngModelChange)="onSalaryFieldChange()"
                     min="0"
                     step="0.01"
                     placeholder="Enter overtime amount">
            </div>

            <!-- Other Deductions -->
            <div class="mb-3">
              <label class="form-label">Other Deductions</label>
              <input type="number"
                     class="form-control"
                     name="otherDeductions"
                     [(ngModel)]="payrollRecord.otherDeductions"
                     (ngModelChange)="onSalaryFieldChange()"
                     min="0"
                     step="0.01"
                     placeholder="Enter other deductions">
            </div>

            <!-- Salary Summary -->
            <div class="card bg-light mb-3" *ngIf="payrollRecord.basicSalary > 0">
              <div class="card-body">
                <h6 class="card-title">Salary Summary</h6>
                <table class="table table-sm table-borderless">
                  <tr>
                    <td>Gross Salary:</td>
                    <td class="text-end"><strong>{{ formatCurrency(payrollRecord.grossSalary) }}</strong></td>
                  </tr>
                  <tr>
                    <td>EPF Employee ({{ epfEmployeeRate }}%):</td>
                    <td class="text-end">{{ formatCurrency(payrollRecord.epfEmployee) }}</td>
                  </tr>
                  <tr>
                    <td>Income Tax:</td>
                    <td class="text-end">{{ formatCurrency(payrollRecord.incomeTax) }}</td>
                  </tr>
                  <tr>
                    <td>Salary Advance Deduction:</td>
                    <td class="text-end">{{ formatCurrency(payrollRecord.salaryAdvanceDeduction) }}</td>
                  </tr>
                  <tr>
                    <td>Other Deductions:</td>
                    <td class="text-end">{{ formatCurrency(payrollRecord.otherDeductions) }}</td>
                  </tr>
                  <tr class="table-active">
                    <td><strong>Total Deductions:</strong></td>
                    <td class="text-end"><strong>{{ formatCurrency(payrollRecord.totalDeductions) }}</strong></td>
                  </tr>
                  <tr class="table-success">
                    <td><strong>Net Salary:</strong></td>
                    <td class="text-end"><strong>{{ formatCurrency(payrollRecord.netSalary) }}</strong></td>
                  </tr>
                </table>
              </div>
            </div>

            <!-- Salary Advances -->
            <div class="card bg-warning bg-opacity-10 mb-3" *ngIf="payrollRecord.salaryAdvances.length > 0">
              <div class="card-body">
                <h6 class="card-title">Salary Advance Deductions</h6>
                <div class="table-responsive">
                  <table class="table table-sm">
                    <thead>
                      <tr>
                        <th>Request Date</th>
                        <th>Advance Amount</th>
                        <th>Reason</th>
                        <th>Deduction Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let advance of payrollRecord.salaryAdvances">
                        <td>{{ formatDate(advance.requestDate) }}</td>
                        <td>{{ formatCurrency(advance.advanceAmount) }}</td>
                        <td>{{ advance.reason || 'N/A' }}</td>
                        <td>{{ formatCurrency(advance.remainingAmount) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="alert alert-info mt-2">
                  <strong>Total Advance Deduction:</strong> {{ formatCurrency(payrollRecord.salaryAdvanceDeduction) }}
                </div>
              </div>
            </div>

            <!-- Buttons -->
            <div class="d-flex gap-2 justify-content-end">
              <button type="button" class="btn btn-outline-secondary" (click)="clear()">
                <i class="fas fa-times me-1"></i>Clear
              </button>
              <button type="submit"
                      class="btn btn-primary"
                      [disabled]="!payrollForm.valid || !selectedEmployee"
                      (click)="processPayroll(payrollForm)">
                <i class="fas fa-calculator me-1"></i>Process Payroll
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Payroll Records -->
    <div class="col-lg-6">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">Payroll Records</h5>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive" style="min-height: 400px;">
            <table class="table table-striped table-hover mb-0">
              <thead class="table-light">
                <tr>
                  <th>Employee</th>
                  <th>Pay Period</th>
                  <th>Net Salary</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let record of payrollRecords; let i = index"
                    [class.table-active]="selectedPayrollRecord?.id === record.id"
                    (click)="selectPayrollRecord(record, i)"
                    style="cursor: pointer;">
                  <td>
                    <div>
                      <strong>{{ record.employee?.name || 'N/A' }}</strong>
                      <br>
                      <small class="text-muted">{{ record.employee?.epfNo || 'N/A' }}</small>
                    </div>
                  </td>
                  <td>{{ formatDate(record.payPeriod) }}</td>
                  <td>{{ formatCurrency(record.netSalary) }}</td>
                  <td>
                    <span class="badge" [class]="getStatusClass(record.status)">
                      {{ record.status }}
                    </span>
                  </td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary btn-sm"
                              (click)="printSalarySlip(); $event.stopPropagation()"
                              title="Print Salary Slip">
                        <i class="fas fa-print"></i>
                      </button>
                      <button class="btn btn-outline-success btn-sm"
                              *ngIf="record.status === 'PROCESSED'"
                              (click)="markAsPaid(); $event.stopPropagation()"
                              title="Mark as Paid">
                        <i class="fas fa-check"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                <tr *ngIf="payrollRecords.length === 0">
                  <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-inbox fa-2x mb-2"></i>
                    <br>
                    No payroll records found
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Selected Payroll Details -->
  <div class="row mt-4" *ngIf="selectedPayrollRecord">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Payroll Details</h5>
            <button class="btn btn-primary btn-sm" (click)="printSalarySlip()">
              <i class="fas fa-print me-1"></i>Print Salary Slip
            </button>
          </div>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Employee Information</h6>
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Name:</strong></td>
                  <td>{{ selectedPayrollRecord.employee?.name }}</td>
                </tr>
                <tr>
                  <td><strong>EPF No:</strong></td>
                  <td>{{ selectedPayrollRecord.employee?.epfNo }}</td>
                </tr>
                <tr>
                  <td><strong>Department:</strong></td>
                  <td>{{ selectedPayrollRecord.employee?.department?.departmentName }}</td>
                </tr>
                <tr>
                  <td><strong>Designation:</strong></td>
                  <td>{{ selectedPayrollRecord.employee?.designation?.designationName }}</td>
                </tr>
                <tr>
                  <td><strong>Pay Period:</strong></td>
                  <td>{{ formatDate(selectedPayrollRecord.payPeriod) }}</td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6>Salary Breakdown</h6>
              <table class="table table-borderless table-sm">
                <tr>
                  <td><strong>Basic Salary:</strong></td>
                  <td class="text-end">{{ formatCurrency(selectedPayrollRecord.basicSalary) }}</td>
                </tr>
                <tr>
                  <td><strong>Allowances:</strong></td>
                  <td class="text-end">{{ formatCurrency(selectedPayrollRecord.allowances) }}</td>
                </tr>
                <tr>
                  <td><strong>Overtime:</strong></td>
                  <td class="text-end">{{ formatCurrency(selectedPayrollRecord.overtime) }}</td>
                </tr>
                <tr class="table-active">
                  <td><strong>Gross Salary:</strong></td>
                  <td class="text-end"><strong>{{ formatCurrency(selectedPayrollRecord.grossSalary) }}</strong></td>
                </tr>
                <tr>
                  <td><strong>Total Deductions:</strong></td>
                  <td class="text-end">{{ formatCurrency(selectedPayrollRecord.totalDeductions) }}</td>
                </tr>
                <tr class="table-success">
                  <td><strong>Net Salary:</strong></td>
                  <td class="text-end"><strong>{{ formatCurrency(selectedPayrollRecord.netSalary) }}</strong></td>
                </tr>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
