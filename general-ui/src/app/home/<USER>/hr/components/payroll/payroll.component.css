.card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  border: 1px solid #e0e0e0;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-top: none;
}

.table td {
  vertical-align: middle;
}

.table-active {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
  border-left: 3px solid #2196f3 !important;
}

.table-success {
  background-color: #d4edda !important;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-primary {
  background-color: #007bff;
  color: white;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.gap-2 {
  gap: 0.5rem;
}

.text-muted {
  color: #6c757d !important;
}

.text-end {
  text-align: right !important;
}

.fa-2x {
  font-size: 2em;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.me-1 {
  margin-right: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.table-borderless td {
  border: none;
}

.table-sm td {
  padding: 0.3rem;
}

.bg-opacity-10 {
  background-color: rgba(255, 193, 7, 0.1) !important;
}

.d-flex {
  display: flex !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.text-center {
  text-align: center !important;
}

/* Salary slip specific styles */
.salary-slip {
  font-family: Arial, sans-serif;
  margin: 20px;
}

.salary-slip .header {
  text-align: center;
  border-bottom: 2px solid #000;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.salary-slip .employee-info {
  margin-bottom: 20px;
}

.salary-slip .salary-table {
  width: 100%;
  border-collapse: collapse;
}

.salary-slip .salary-table th,
.salary-slip .salary-table td {
  border: 1px solid #000;
  padding: 8px;
  text-align: left;
}

.salary-slip .salary-table th {
  background-color: #f0f0f0;
}

.salary-slip .total-row {
  font-weight: bold;
  background-color: #f9f9f9;
}

@media print {
  .btn-group {
    display: none !important;
  }
  
  .card {
    border: none !important;
    box-shadow: none !important;
  }
  
  .card-header {
    background-color: transparent !important;
    border-bottom: 2px solid #000 !important;
  }
}
