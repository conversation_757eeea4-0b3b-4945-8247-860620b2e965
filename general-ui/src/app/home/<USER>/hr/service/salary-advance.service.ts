import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SalaryAdvance } from '../model/salary-advance';
import { HrApiConstants } from '../hr-constants';

@Injectable({
  providedIn: 'root'
})
export class SalaryAdvanceService {

  constructor(private http: HttpClient) { }

  save(salaryAdvance: SalaryAdvance): Observable<any> {
    return this.http.post(HrApiConstants.SAVE_SALARY_ADVANCE, salaryAdvance);
  }

  findAll(page: number, pageSize: number): Observable<any> {
    return this.http.get(HrApiConstants.FIND_ALL_SALARY_ADVANCES, {
      params: { page: page.toString(), pageSize: pageSize.toString() }
    });
  }

  findById(id: string): Observable<SalaryAdvance> {
    return this.http.get<SalaryAdvance>(HrApiConstants.FIND_SALARY_ADVANCE_BY_ID, {
      params: { id: id }
    });
  }

  findByEmployee(employeeId: string): Observable<SalaryAdvance[]> {
    return this.http.get<SalaryAdvance[]>(HrApiConstants.FIND_SALARY_ADVANCES_BY_EMPLOYEE, {
      params: { employeeId: employeeId }
    });
  }

  findByStatus(status: string): Observable<SalaryAdvance[]> {
    return this.http.get<SalaryAdvance[]>(HrApiConstants.FIND_SALARY_ADVANCES_BY_STATUS, {
      params: { status: status }
    });
  }

  approve(id: string, approvedBy: string): Observable<any> {
    return this.http.post(HrApiConstants.APPROVE_SALARY_ADVANCE, {
      id: id,
      approvedBy: approvedBy
    });
  }

  reject(id: string, remarks: string): Observable<any> {
    return this.http.post(HrApiConstants.REJECT_SALARY_ADVANCE, {
      id: id,
      remarks: remarks
    });
  }

  markAsPaid(id: string): Observable<any> {
    return this.http.post(HrApiConstants.MARK_SALARY_ADVANCE_PAID, { id: id });
  }

  getPendingAdvances(): Observable<SalaryAdvance[]> {
    return this.http.get<SalaryAdvance[]>(HrApiConstants.GET_PENDING_SALARY_ADVANCES);
  }

  getAdvancesByEmployee(employeeId: string): Observable<SalaryAdvance[]> {
    return this.http.get<SalaryAdvance[]>(HrApiConstants.GET_ADVANCES_BY_EMPLOYEE, {
      params: { employeeId: employeeId }
    });
  }
}
