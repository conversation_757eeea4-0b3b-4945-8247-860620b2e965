import { Employee } from './employee';
import { SalaryAdvance } from './salary-advance';

export class PayrollRecord {
  public id: string;
  public employee: Employee;
  public payPeriod: Date;
  public basicSalary: number;
  public allowances: number;
  public overtime: number;
  public grossSalary: number;
  public epfEmployee: number;
  public epfEmployer: number;
  public etfEmployer: number;
  public incomeTax: number;
  public salaryAdvanceDeduction: number;
  public otherDeductions: number;
  public totalDeductions: number;
  public netSalary: number;
  public status: string; // DRAFT, PROCESSED, PAID
  public processedDate: Date;
  public paidDate: Date;
  public remarks: string;
  public active: boolean;
  
  // Salary advance details
  public salaryAdvances: SalaryAdvance[];
  
  constructor() {
    this.allowances = 0;
    this.overtime = 0;
    this.epfEmployee = 0;
    this.epfEmployer = 0;
    this.etfEmployer = 0;
    this.incomeTax = 0;
    this.salaryAdvanceDeduction = 0;
    this.otherDeductions = 0;
    this.status = 'DRAFT';
    this.active = true;
    this.salaryAdvances = [];
  }
}
