import { Employee } from './employee';

export class SalaryAdvance {
  public id: string;
  public employee: Employee;
  public advanceAmount: number;
  public requestDate: Date;
  public approvedDate: Date;
  public reason: string;
  public status: string; // PENDING, APPROVED, REJECTED, PAID
  public approvedBy: string;
  public paidDate: Date;
  public deductionStartMonth: Date;
  public numberOfInstallments: number;
  public installmentAmount: number;
  public remainingAmount: number;
  public active: boolean;
  public remarks: string;
}
