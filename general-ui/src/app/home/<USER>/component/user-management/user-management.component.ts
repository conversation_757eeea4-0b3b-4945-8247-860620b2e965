import {Component, OnInit, TemplateRef} from '@angular/core';
import {User} from '../../model/user';
import {Role} from '../../model/role';
import {NotificationService} from '../../../core/service/notification.service';
import {UserService} from '../../service/user.service';
import {RoleService} from '../../service/role.service';
import {BsModalRef, ModalOptions} from 'ngx-bootstrap/modal';
import {BsModalService} from 'ngx-bootstrap/modal';
import {Permission} from '../../model/permission';
import {PermissionService} from '../../service/permission.service';
import {Module} from '../../model/module';
import {CreateUserComponent} from "../create-user/create-user.component";
import {ManageUserPermissionsComponent} from "../user-permissions/manage-user-permissions.component";
import {ManageUserSettingsComponent} from "../manage-user-settings/manage-user-settings.component";

@Component({
  standalone: false,
  selector: 'app-user',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css']
})
export class UserManagementComponent implements OnInit {

  search: any;
  user = new User();
  users: Array<User> = [];
  userRoles: Array<Role> = [];
  confirmPassword: string;
  selectedRow: number;
  setClickedRow: Function;
  modalRef: BsModalRef;
  availablePerms: Array<Permission> = [];
  selectedPerms: Array<Permission> = [];
  modules: Array<Module> = [];
  selectedUser: User;
  isUserSelected: boolean;

  ngOnInit() {
    this.user = new User();
    this.selectedUser = new User();
    this.user.userRoles = [];
    this.isUserSelected = false;
    this.findAllUsers();
    this.findAllRole();
    this.getEnabledModulesForUser();
  }

  constructor(private userService: UserService, private notificationService: NotificationService,
              private roleService: RoleService, private permService: PermissionService,
              private modalService: BsModalService) {
  }

  getEnabledModulesForUser() {
    this.permService.getEnabledModules(this.user.username).subscribe((result: Array<Module>) => {
      this.modules = result;
    });
  }

  findAllUsers() {
    this.userService.findAll().subscribe((data: Array<User>) => {
      this.users = data;
    });
  }

  findAllRole() {
    this.roleService.findAll().subscribe((data: Array<Role>) => {
      this.userRoles = data;
    });
  }

  selectRole(item: Role) {
    let available = false;
    for (const i of this.user.userRoles) {
      if (i.id === item.id) {
        available = true;
      }
    }
    if (!available) {
      this.user.userRoles.push(item);
    }
  }

  userDetail(selectedItem: User, index) {
    this.selectedRow = index;
    this.selectedUser = selectedItem;
    this.isUserSelected = true;
    this.user.password = 'NOCHNG';
    this.confirmPassword = 'NOCHNG';
  }

  searchUser() {
    this.users = [];
    this.userService.searchByName(this.search).subscribe((data: User) => {
      this.users.push(data);
    });
  }

  editUser(templateEditUser: TemplateRef<any>) {
    this.modalRef = this.modalService.show(CreateUserComponent, <ModalOptions>{
      class: 'modal-xl',
      initialState: {
        isModal: true
      }
    });
    this.modalRef.content.user = this.selectedUser;
    this.modalRef.content.modalRef = this.modalRef;
    this.modalRef.content.user.password = 'NOCHNG';
    this.modalRef.content.confirmPassword = 'NOCHNG';
    this.modalRef.content.isPasswordMatch = true;
    this.modalRef.content.permissions = this.selectedUser.permissions;
    this.modalRef.content.isModal = true;

    // Use a subscription variable to properly clean up
    const editUserSubscription = this.modalService.onHide.subscribe(() => {
      this.ngOnInit();
      // Clean up subscription to avoid memory leaks
      editUserSubscription.unsubscribe();
    });
  }

  /**
   * Opens the ManageUserPermissionsComponent as a modal to edit the selected user's permissions
   */
  editUserPermissions() {
    if (!this.selectedUser || !this.selectedUser.username) {
      this.notificationService.showWarning('Please select a user first');
      return;
    }

    this.modalRef = this.modalService.show(ManageUserPermissionsComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true,
        selectedUsername: this.selectedUser.username,
        userName: this.selectedUser.username
      }
    });

    // Pass the modalRef to the component so it can close itself
    this.modalRef.content.modalRef = this.modalRef;

    // Use a subscription variable to properly clean up
    const permissionsSubscription = this.modalService.onHide.subscribe(() => {
      this.ngOnInit();
      // Clean up subscription to avoid memory leaks
      permissionsSubscription.unsubscribe();
    });
  }

  /**
   * Opens the ManageUserSettingsComponent as a modal to manage the selected user's settings
   */
  manageUserSettings() {
    if (!this.selectedUser || !this.selectedUser.username) {
      this.notificationService.showWarning('Please select a user first');
      return;
    }

    this.modalRef = this.modalService.show(ManageUserSettingsComponent, <ModalOptions>{
      class: 'modal-lg',
      initialState: {
        isModal: true,
        username: this.selectedUser.username
      }
    });

    // Pass the modalRef to the component so it can close itself
    this.modalRef.content.modalRef = this.modalRef;

    // Ensure isModal is set to true
    this.modalRef.content.isModal = true;

    // Use a subscription variable to properly clean up
    const settingsSubscription = this.modalService.onHide.subscribe(() => {
      this.ngOnInit();
      // Clean up subscription to avoid memory leaks
      settingsSubscription.unsubscribe();
    });
  }
}

