import { Component, OnInit } from '@angular/core';
import { Route } from '../../model/route';
import { RouteService } from '../../service/route.service';
import { ToastrService } from 'ngx-toastr';

@Component({
  standalone: false,
  selector: 'app-route-management',
  templateUrl: './route-management.component.html',
  styleUrls: ['./route-management.component.css']
})
export class RouteManagementComponent implements OnInit {
  // Data
  route = new Route();
  routes: Route[] = [];
  selectedRow: number;

  // Pagination
  totalElements: number;
  currentPage: number;
  pageSize: number;

  // Search
  searchTerm: string = '';

  // Loading state
  loading: boolean = false;

  constructor(
    private routeService: RouteService,
    private toastr: ToastrService
  ) { }

  ngOnInit(): void {
    this.currentPage = 1;
    this.pageSize = 10;
    this.route = new Route();
    this.route.active = true;
    this.findAll();
  }

  /**
   * Load routes with pagination
   */
  loadRoutes(): void {
    this.routeService.searchByName(this.searchTerm, 0, 15).subscribe((data: any) => {
      return this.routes = data.content;
    }, (error) => {
      console.error('Error searching routes:', error);
      this.toastr.error('Failed to search routes', 'Error');
    });
  }

  /**
   * Find all routes with pagination
   */
  findAll(): void {
    this.loading = true;
    this.routeService.findAll(this.currentPage - 1, this.pageSize).subscribe(
      (data: any) => {
        this.routes = data.content;
        this.totalElements = data.totalElements;
        this.loading = false;
      },
      (error) => {
        console.error('Error loading routes:', error);
        this.toastr.error('Failed to load routes', 'Error');
        this.loading = false;
      }
    );
  }

  /**
   * Handle page change
   * @param event Page change event
   */
  pageChanged(event: any): void {
    this.currentPage = event.page;
    this.findAll();
  }

  /**
   * Cancel editing
   */
  cancelEdit(): void {
    this.route = new Route();
    this.route.active = true;
  }

  /**
   * Save or update a route
   */
  saveRoute(): void {
    this.loading = true;
    this.routeService.save(this.route).subscribe(
      (response) => {
        if (response.success) {
          this.toastr.success(response.message, 'Success');
          this.findAll();
          this.route = new Route();
          this.route.active = true;
        } else {
          this.toastr.error(response.message, 'Error');
        }
        this.loading = false;
      },
      (error) => {
        console.error('Error saving route:', error);
        this.toastr.error('Failed to save route', 'Error');
        this.loading = false;
      }
    );
  }

  /**
   * Select a route
   * @param event Event or route object
   * @param index Index of the route in the list
   */
  selectRoute(event: any, index?: number): void {
    if (event && event.item) {
      // Handle typeahead selection
      this.route = {...event.item};
    } else if (event) {
      // Handle table row selection
      this.route = {...event};
      this.selectedRow = index;
    }
  }
}
