import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DashboardRoutingModule, dashboardRouteParams } from './dashboard-routing.module';
import { DashboardService } from './service/dashboard.service';
import { CoreModule } from '../../core/core.module';

@NgModule({
  declarations: [dashboardRouteParams],
  imports: [
    CommonModule,
    CoreModule,
    DashboardRoutingModule
  ],
  providers: [
    DashboardService
  ]
})
export class DashboardModule { }
