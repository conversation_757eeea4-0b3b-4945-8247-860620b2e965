import { Component, OnInit } from '@angular/core';
import { TranslateService } from './translate.service';

@Component({
  standalone: false,
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  constructor(private translateService: TranslateService) {}

  ngOnInit() {
    // Get the current language from localStorage
    const currentLang = localStorage.getItem('lang') || 'en';
    console.log(`AppComponent initialized with language: ${currentLang}`);

    // Ensure the correct language is loaded
    this.translateService.use(currentLang).then(() => {
      console.log(`Language loaded in AppComponent: ${currentLang}`);

      // Debug translations
      this.translateService.debugTranslations();
    }).catch(error => {
      console.error('Error loading language in AppComponent:', error);
    });
  }
}
